/**
 * SEO Utilities for AITELMED Service Pages
 * 
 * This file contains utilities for implementing comprehensive SEO optimization
 * including meta tags, structured data, Open Graph, and Twitter Cards.
 */

import type { Metadata } from 'next';

/**
 * Base SEO configuration for AITELMED
 */
export const baseSEOConfig = {
  siteName: 'AITELMED',
  siteUrl: 'https://aitelmed.com',
  defaultTitle: 'AITELMED - GBA-konforme Telemedizin & medPower® Plattform',
  defaultDescription: 'Führende Telemedizin-Lösungen mit der medPower® Plattform. GBA-konform, MDR-zertifiziert und DSGVO-konform für deutsche Gesundheitseinrichtungen.',
  defaultKeywords: 'Telemedizin, medPower, GBA-konform, MDR-zertifiziert, Telemonitoring, IoMT, Digitalisierung, Gesundheitswesen',
  twitterHandle: '@aitelmed',
  locale: 'de_DE',
  type: 'website'
} as const;

/**
 * Service-specific SEO configurations
 */
export const serviceSEOConfig = {
  iomt: {
    title: 'IoMT Cybersecurity - Medizingeräte-Sicherheit | AITELMED',
    description: 'Professionelle IoMT-Cybersecurity-Lösungen für Gesundheitseinrichtungen. Medizingeräte-Sicherheit, Schwachstellen-Management und GBA-konforme Compliance.',
    keywords: 'IoMT, Internet of Medical Things, Medizingeräte-Sicherheit, Cybersecurity, Healthcare, GBA-konform, DSGVO',
    path: '/services/iomt',
    category: 'Cybersecurity',
    serviceType: 'IoMT Security Solutions'
  },
  digitalization: {
    title: 'Digitalisierung von Gesundheitsprozessen | AITELMED',
    description: 'Digitalisierung von Pflegeprozessen: Schwesternrufanlagen, Pflegemanagement, Telepflege, Demenz-Care und Patientenmanagement. GBA-konform und DSGVO-konform.',
    keywords: 'Digitalisierung, Gesundheitsprozesse, Schwesternrufanlage, Pflegemanagement, Telepflege, Demenz Care, Patientenmanagement',
    path: '/services/digitalisierung',
    category: 'Healthcare Digitalization',
    serviceType: 'Digital Healthcare Solutions'
  },
  telemonitoring: {
    title: 'Telemonitoring & medPower® Plattform | AITELMED',
    description: 'Professionelle Telemonitoring-Lösungen mit der medPower® Plattform. GBA-konform, MDR-zertifiziert und DSGVO-konform für deutsche Gesundheitseinrichtungen.',
    keywords: 'Telemonitoring, medPower, Telemedizin, GBA-konform, MDR-zertifiziert, Fernüberwachung, Patientenmonitoring',
    path: '/services/telemonitoring',
    category: 'Telemonitoring',
    serviceType: 'Telemonitoring Platform'
  },
  additionalServices: {
    title: 'Weitere Angebote - Smart Home & Sicherheitssysteme | AITELMED',
    description: 'Intelligente Lösungen für Pflegeheime: Smart Home Systeme, Videoüberwachung und Alarmsysteme. Sicherheit und Komfort für Bewohner und Personal.',
    keywords: 'Smart Home, Pflegeheime, Videoüberwachung, Alarmsysteme, Sicherheitstechnik, Intelligente Gebäude',
    path: '/services/weitere-angebote',
    category: 'Smart Home & Security',
    serviceType: 'Smart Care Solutions'
  }
} as const;

/**
 * Generate comprehensive metadata for service pages
 */
export function generateServiceMetadata(
  service: keyof typeof serviceSEOConfig,
  locale: string = 'de'
): Metadata {
  const config = serviceSEOConfig[service];
  const baseUrl = baseSEOConfig.siteUrl;
  const fullUrl = `${baseUrl}${config.path}`;
  
  return {
    title: config.title,
    description: config.description,
    keywords: config.keywords,
    
    // Open Graph
    openGraph: {
      title: config.title,
      description: config.description,
      url: fullUrl,
      siteName: baseSEOConfig.siteName,
      locale: locale === 'de' ? 'de_DE' : locale === 'en' ? 'en_US' : 'pl_PL',
      type: 'website',
      images: [
        {
          url: `${baseUrl}/images/og/${service}-og.jpg`,
          width: 1200,
          height: 630,
          alt: config.title,
        }
      ],
    },
    
    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      site: baseSEOConfig.twitterHandle,
      creator: baseSEOConfig.twitterHandle,
      title: config.title,
      description: config.description,
      images: [`${baseUrl}/images/twitter/${service}-twitter.jpg`],
    },
    
    // Additional meta tags
    other: {
      'application-name': baseSEOConfig.siteName,
      'apple-mobile-web-app-title': baseSEOConfig.siteName,
      'format-detection': 'telephone=no',
      'mobile-web-app-capable': 'yes',
      'msapplication-TileColor': '#2563eb',
      'theme-color': '#2563eb',
    },
    
    // Robots
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    
    // Canonical URL
    alternates: {
      canonical: fullUrl,
      languages: {
        'de': `${baseUrl}/de${config.path}`,
        'en': `${baseUrl}/en${config.path}`,
        'pl': `${baseUrl}/pl${config.path}`,
      },
    },
  };
}

/**
 * Generate JSON-LD structured data for service pages
 */
export function generateServiceStructuredData(
  service: keyof typeof serviceSEOConfig,
  locale: string = 'de'
) {
  const config = serviceSEOConfig[service];
  const baseUrl = baseSEOConfig.siteUrl;
  const fullUrl = `${baseUrl}${config.path}`;
  
  // Organization schema
  const organization = {
    '@type': 'Organization',
    '@id': `${baseUrl}/#organization`,
    name: 'AITELMED',
    url: baseUrl,
    logo: {
      '@type': 'ImageObject',
      url: `${baseUrl}/images/logo/aitelmed-logo.png`,
      width: 300,
      height: 100,
    },
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+49-***********',
      contactType: 'customer service',
      availableLanguage: ['German', 'English', 'Polish'],
    },
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'DE',
      addressLocality: 'Berlin',
      postalCode: '10115',
      streetAddress: 'Musterstraße 123',
    },
    sameAs: [
      'https://linkedin.com/company/aitelmed',
      'https://twitter.com/aitelmed',
    ],
  };

  // Service schema
  const serviceSchema = {
    '@type': 'Service',
    '@id': `${fullUrl}/#service`,
    name: config.title,
    description: config.description,
    url: fullUrl,
    provider: {
      '@id': `${baseUrl}/#organization`,
    },
    serviceType: config.serviceType,
    category: config.category,
    areaServed: {
      '@type': 'Country',
      name: 'Germany',
    },
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: `${config.serviceType} Catalog`,
      itemListElement: getServiceOffers(service),
    },
  };

  // WebPage schema
  const webPageSchema = {
    '@type': 'WebPage',
    '@id': `${fullUrl}/#webpage`,
    url: fullUrl,
    name: config.title,
    description: config.description,
    isPartOf: {
      '@id': `${baseUrl}/#website`,
    },
    about: {
      '@id': `${fullUrl}/#service`,
    },
    primaryImageOfPage: {
      '@type': 'ImageObject',
      url: `${baseUrl}/images/og/${service}-og.jpg`,
    },
    datePublished: '2024-01-01',
    dateModified: new Date().toISOString().split('T')[0],
    inLanguage: locale,
  };

  // Website schema
  const websiteSchema = {
    '@type': 'WebSite',
    '@id': `${baseUrl}/#website`,
    url: baseUrl,
    name: baseSEOConfig.siteName,
    description: baseSEOConfig.defaultDescription,
    publisher: {
      '@id': `${baseUrl}/#organization`,
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${baseUrl}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
    inLanguage: ['de', 'en', 'pl'],
  };

  return {
    '@context': 'https://schema.org',
    '@graph': [organization, serviceSchema, webPageSchema, websiteSchema],
  };
}

/**
 * Get service-specific offers for structured data
 */
function getServiceOffers(service: keyof typeof serviceSEOConfig) {
  const offers = {
    iomt: [
      {
        '@type': 'Offer',
        name: 'IoMT Device Security',
        description: 'Comprehensive security for medical devices',
      },
      {
        '@type': 'Offer',
        name: 'Vulnerability Management',
        description: 'Proactive vulnerability assessment and management',
      },
      {
        '@type': 'Offer',
        name: 'Compliance Monitoring',
        description: 'GBA-compliant security monitoring',
      },
    ],
    digitalization: [
      {
        '@type': 'Offer',
        name: 'Nurse Call Systems',
        description: 'Digital nurse call and communication systems',
      },
      {
        '@type': 'Offer',
        name: 'Care Management',
        description: 'Digital care process management',
      },
      {
        '@type': 'Offer',
        name: 'Patient Management',
        description: 'Comprehensive patient management solutions',
      },
    ],
    telemonitoring: [
      {
        '@type': 'Offer',
        name: 'medPower® Platform',
        description: 'GBA-compliant telemonitoring platform',
      },
      {
        '@type': 'Offer',
        name: 'Remote Patient Monitoring',
        description: '24/7 patient monitoring solutions',
      },
      {
        '@type': 'Offer',
        name: 'Telemedicine Integration',
        description: 'Seamless telemedicine integration',
      },
    ],
    additionalServices: [
      {
        '@type': 'Offer',
        name: 'Smart Home Systems',
        description: 'Intelligent home automation for care facilities',
      },
      {
        '@type': 'Offer',
        name: 'Video Surveillance',
        description: 'Professional security monitoring systems',
      },
      {
        '@type': 'Offer',
        name: 'Alarm Systems',
        description: 'Comprehensive alarm and safety systems',
      },
    ],
  };

  return offers[service] || [];
}

/**
 * Generate breadcrumb structured data
 */
export function generateBreadcrumbStructuredData(
  service: keyof typeof serviceSEOConfig,
  locale: string = 'de'
) {
  const config = serviceSEOConfig[service];
  const baseUrl = baseSEOConfig.siteUrl;
  
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: `${baseUrl}/${locale}`,
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'Services',
        item: `${baseUrl}/${locale}/services`,
      },
      {
        '@type': 'ListItem',
        position: 3,
        name: config.title.split(' | ')[0],
        item: `${baseUrl}/${locale}${config.path}`,
      },
    ],
  };
}
