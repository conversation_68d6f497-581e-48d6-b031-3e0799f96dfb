/**
 * Responsive Design Utilities for AITELMED Service Pages
 * 
 * This file contains utility functions and constants for implementing
 * consistent responsive design patterns across all service pages.
 * 
 * Design System:
 * - Mobile-first approach
 * - WCAG 2.1 AA compliance
 * - AITELMED blue branding consistency
 * - Consistent spacing and typography
 */

/**
 * Breakpoint definitions following mobile-first approach
 * Based on user requirements: 320px-640px mobile, 640px-1024px tablet, 1024px+ desktop
 */
export const breakpoints = {
  mobile: {
    min: 320,
    max: 639
  },
  tablet: {
    min: 640,
    max: 1023
  },
  desktop: {
    min: 1024,
    max: Infinity
  }
} as const;

/**
 * Responsive container classes for consistent layout
 */
export const containerClasses = {
  base: "container mx-auto px-4 sm:px-6 lg:px-8",
  narrow: "container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl",
  wide: "container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl"
} as const;

/**
 * Responsive spacing utilities following 8px grid system
 */
export const spacing = {
  section: {
    mobile: "py-16",
    tablet: "sm:py-20",
    desktop: "lg:py-24"
  },
  component: {
    mobile: "py-8",
    tablet: "sm:py-12",
    desktop: "lg:py-16"
  },
  element: {
    mobile: "py-4",
    tablet: "sm:py-6",
    desktop: "lg:py-8"
  }
} as const;

/**
 * Typography scale for responsive headings
 */
export const typography = {
  hero: {
    mobile: "text-4xl",
    tablet: "sm:text-5xl",
    desktop: "lg:text-6xl"
  },
  section: {
    mobile: "text-3xl",
    tablet: "sm:text-4xl",
    desktop: "lg:text-5xl"
  },
  subsection: {
    mobile: "text-2xl",
    tablet: "sm:text-3xl",
    desktop: "lg:text-4xl"
  },
  body: {
    large: "text-lg sm:text-xl",
    base: "text-base sm:text-lg",
    small: "text-sm sm:text-base"
  }
} as const;

/**
 * AITELMED brand colors for consistent theming
 */
export const brandColors = {
  primary: {
    50: "bg-blue-50 dark:bg-blue-950",
    100: "bg-blue-100 dark:bg-blue-900",
    200: "bg-blue-200 dark:bg-blue-800",
    600: "bg-blue-600",
    700: "bg-blue-700"
  },
  text: {
    primary: "text-blue-600 dark:text-blue-400",
    secondary: "text-blue-700 dark:text-blue-300"
  },
  border: {
    light: "border-blue-200 dark:border-blue-800",
    medium: "border-blue-300 dark:border-blue-700"
  }
} as const;

/**
 * Service-specific color schemes
 */
export const serviceColors = {
  iomt: {
    primary: "blue",
    gradient: "from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900"
  },
  digitalization: {
    primary: "green",
    gradient: "from-green-50 to-green-100 dark:from-green-950 dark:to-green-900"
  },
  telemonitoring: {
    primary: "blue",
    gradient: "from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900"
  },
  additionalServices: {
    primary: "purple",
    gradient: "from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900"
  }
} as const;

/**
 * Accessibility utilities for WCAG 2.1 AA compliance
 */
export const accessibility = {
  minTouchTarget: "min-h-[44px] min-w-[44px]",
  focusRing: "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
  skipLink: "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50",
  visuallyHidden: "sr-only",
  highContrast: "contrast-more:border-2 contrast-more:border-current"
} as const;

/**
 * Grid system utilities for consistent layouts
 */
export const grid = {
  cards: {
    mobile: "grid grid-cols-1",
    tablet: "md:grid-cols-2",
    desktop: "lg:grid-cols-3"
  },
  features: {
    mobile: "grid grid-cols-1",
    tablet: "md:grid-cols-2",
    desktop: "lg:grid-cols-4"
  },
  stats: {
    mobile: "grid grid-cols-2",
    tablet: "md:grid-cols-4",
    desktop: "lg:grid-cols-4"
  }
} as const;

/**
 * Animation utilities for smooth interactions
 */
export const animations = {
  transition: "transition-all duration-200",
  hover: "hover:shadow-lg hover:scale-105",
  focus: "focus:scale-105",
  fadeIn: "animate-in fade-in duration-500",
  slideUp: "animate-in slide-in-from-bottom-4 duration-500"
} as const;

/**
 * Utility function to combine responsive classes
 */
export function responsiveClasses(...classes: (string | undefined | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

/**
 * Utility function to get service-specific color scheme
 */
export function getServiceColors(service: keyof typeof serviceColors) {
  return serviceColors[service];
}

/**
 * Utility function to create responsive typography classes
 */
export function createResponsiveTypography(scale: keyof typeof typography) {
  const typeScale = typography[scale];
  if (typeof typeScale === 'string') {
    return typeScale;
  }
  return responsiveClasses(
    typeScale.mobile,
    typeScale.tablet,
    typeScale.desktop
  );
}

/**
 * Utility function to create responsive spacing classes
 */
export function createResponsiveSpacing(type: keyof typeof spacing) {
  const spaceScale = spacing[type];
  return responsiveClasses(
    spaceScale.mobile,
    spaceScale.tablet,
    spaceScale.desktop
  );
}

/**
 * Media query utilities for JavaScript
 */
export const mediaQueries = {
  mobile: `(max-width: ${breakpoints.mobile.max}px)`,
  tablet: `(min-width: ${breakpoints.tablet.min}px) and (max-width: ${breakpoints.tablet.max}px)`,
  desktop: `(min-width: ${breakpoints.desktop.min}px)`,
  prefersReducedMotion: '(prefers-reduced-motion: reduce)',
  prefersHighContrast: '(prefers-contrast: high)',
  prefersDarkMode: '(prefers-color-scheme: dark)'
} as const;

/**
 * Utility function to check if device supports hover
 */
export function supportsHover(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(hover: hover)').matches;
}

/**
 * Utility function to check if user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia(mediaQueries.prefersReducedMotion).matches;
}

/**
 * Utility function to get current breakpoint
 */
export function getCurrentBreakpoint(): keyof typeof breakpoints {
  if (typeof window === 'undefined') return 'desktop';
  
  const width = window.innerWidth;
  
  if (width <= breakpoints.mobile.max) return 'mobile';
  if (width <= breakpoints.tablet.max) return 'tablet';
  return 'desktop';
}
