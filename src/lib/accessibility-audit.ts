/**
 * Accessibility Audit Utilities for AITELMED Service Pages
 * 
 * This file contains utilities for auditing and ensuring WCAG 2.1 AA compliance
 * across all service pages. It provides both runtime checks and development tools.
 */

/**
 * WCAG 2.1 AA Requirements Checklist
 */
export const wcagRequirements = {
  // Perceivable
  perceivable: {
    textAlternatives: {
      id: '1.1.1',
      level: 'A',
      description: 'All non-text content has text alternatives',
      check: 'Images have alt text, icons have aria-labels'
    },
    colorContrast: {
      id: '1.4.3',
      level: 'AA',
      description: 'Color contrast ratio of at least 4.5:1 for normal text',
      check: 'Text meets contrast requirements'
    },
    resizeText: {
      id: '1.4.4',
      level: 'AA',
      description: 'Text can be resized up to 200% without loss of functionality',
      check: 'Layout remains functional at 200% zoom'
    }
  },
  
  // Operable
  operable: {
    keyboardAccessible: {
      id: '2.1.1',
      level: 'A',
      description: 'All functionality available via keyboard',
      check: 'All interactive elements are keyboard accessible'
    },
    noKeyboardTrap: {
      id: '2.1.2',
      level: 'A',
      description: 'Keyboard focus is not trapped',
      check: 'Users can navigate away from any component'
    },
    targetSize: {
      id: '2.5.5',
      level: 'AAA',
      description: 'Target size is at least 44x44 CSS pixels',
      check: 'All touch targets meet minimum size requirements'
    }
  },
  
  // Understandable
  understandable: {
    languageOfPage: {
      id: '3.1.1',
      level: 'A',
      description: 'Language of page is programmatically determined',
      check: 'HTML lang attribute is set correctly'
    },
    consistentNavigation: {
      id: '3.2.3',
      level: 'AA',
      description: 'Navigation is consistent across pages',
      check: 'Navigation structure is consistent'
    },
    errorIdentification: {
      id: '3.3.1',
      level: 'A',
      description: 'Errors are identified and described to users',
      check: 'Form errors are clearly communicated'
    }
  },
  
  // Robust
  robust: {
    validCode: {
      id: '4.1.1',
      level: 'A',
      description: 'Markup is valid and properly structured',
      check: 'HTML is valid and semantic'
    },
    nameRoleValue: {
      id: '4.1.2',
      level: 'A',
      description: 'Name, role, and value are programmatically determined',
      check: 'All UI components have proper ARIA attributes'
    }
  }
} as const;

/**
 * Accessibility audit results interface
 */
export interface AccessibilityAuditResult {
  passed: boolean;
  issues: AccessibilityIssue[];
  score: number;
  recommendations: string[];
}

export interface AccessibilityIssue {
  severity: 'error' | 'warning' | 'info';
  wcagRule: string;
  element?: string;
  description: string;
  suggestion: string;
}

/**
 * Runtime accessibility checker for development
 */
export class AccessibilityAuditor {
  private issues: AccessibilityIssue[] = [];

  /**
   * Audit a page for accessibility issues
   */
  auditPage(container: HTMLElement = document.body): AccessibilityAuditResult {
    this.issues = [];
    
    this.checkImages(container);
    this.checkHeadings(container);
    this.checkButtons(container);
    this.checkLinks(container);
    this.checkForms(container);
    this.checkLandmarks(container);
    this.checkColorContrast(container);
    
    const score = this.calculateScore();
    const recommendations = this.generateRecommendations();
    
    return {
      passed: this.issues.filter(issue => issue.severity === 'error').length === 0,
      issues: this.issues,
      score,
      recommendations
    };
  }

  /**
   * Check images for alt text
   */
  private checkImages(container: HTMLElement): void {
    const images = container.querySelectorAll('img');
    images.forEach((img, index) => {
      if (!img.alt && !img.getAttribute('aria-label') && !img.getAttribute('aria-labelledby')) {
        this.addIssue({
          severity: 'error',
          wcagRule: '1.1.1',
          element: `img[${index}]`,
          description: 'Image missing alt text',
          suggestion: 'Add descriptive alt text or aria-label to the image'
        });
      }
    });
  }

  /**
   * Check heading hierarchy
   */
  private checkHeadings(container: HTMLElement): void {
    const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    
    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));
      
      if (level > previousLevel + 1) {
        this.addIssue({
          severity: 'warning',
          wcagRule: '1.3.1',
          element: `${heading.tagName.toLowerCase()}[${index}]`,
          description: 'Heading level skipped',
          suggestion: 'Use proper heading hierarchy (h1 → h2 → h3, etc.)'
        });
      }
      
      previousLevel = level;
    });
  }

  /**
   * Check buttons for accessibility
   */
  private checkButtons(container: HTMLElement): void {
    const buttons = container.querySelectorAll('button, [role="button"]');
    buttons.forEach((button, index) => {
      // Check for accessible name
      const hasAccessibleName = button.textContent?.trim() || 
                               button.getAttribute('aria-label') || 
                               button.getAttribute('aria-labelledby');
      
      if (!hasAccessibleName) {
        this.addIssue({
          severity: 'error',
          wcagRule: '4.1.2',
          element: `button[${index}]`,
          description: 'Button missing accessible name',
          suggestion: 'Add text content, aria-label, or aria-labelledby to the button'
        });
      }

      // Check touch target size
      const rect = button.getBoundingClientRect();
      if (rect.width < 44 || rect.height < 44) {
        this.addIssue({
          severity: 'warning',
          wcagRule: '2.5.5',
          element: `button[${index}]`,
          description: 'Button touch target too small',
          suggestion: 'Ensure button is at least 44x44 pixels'
        });
      }
    });
  }

  /**
   * Check links for accessibility
   */
  private checkLinks(container: HTMLElement): void {
    const links = container.querySelectorAll('a');
    links.forEach((link, index) => {
      // Check for accessible name
      const hasAccessibleName = link.textContent?.trim() || 
                               link.getAttribute('aria-label') || 
                               link.getAttribute('aria-labelledby');
      
      if (!hasAccessibleName) {
        this.addIssue({
          severity: 'error',
          wcagRule: '4.1.2',
          element: `a[${index}]`,
          description: 'Link missing accessible name',
          suggestion: 'Add descriptive text content or aria-label to the link'
        });
      }

      // Check for external links
      if (link.hostname && link.hostname !== window.location.hostname) {
        if (!link.getAttribute('aria-label')?.includes('external') && 
            !link.textContent?.includes('external')) {
          this.addIssue({
            severity: 'info',
            wcagRule: '3.2.5',
            element: `a[${index}]`,
            description: 'External link not clearly identified',
            suggestion: 'Indicate that link opens in external site'
          });
        }
      }
    });
  }

  /**
   * Check forms for accessibility
   */
  private checkForms(container: HTMLElement): void {
    const formControls = container.querySelectorAll('input, select, textarea');
    formControls.forEach((control, index) => {
      const hasLabel = control.getAttribute('aria-label') || 
                      control.getAttribute('aria-labelledby') ||
                      container.querySelector(`label[for="${control.id}"]`);
      
      if (!hasLabel) {
        this.addIssue({
          severity: 'error',
          wcagRule: '3.3.2',
          element: `${control.tagName.toLowerCase()}[${index}]`,
          description: 'Form control missing label',
          suggestion: 'Add a label element or aria-label to the form control'
        });
      }
    });
  }

  /**
   * Check for proper landmarks
   */
  private checkLandmarks(container: HTMLElement): void {
    const hasMain = container.querySelector('main, [role="main"]');
    if (!hasMain) {
      this.addIssue({
        severity: 'warning',
        wcagRule: '1.3.1',
        element: 'page',
        description: 'Page missing main landmark',
        suggestion: 'Add a main element or role="main" to identify main content'
      });
    }

    const hasNav = container.querySelector('nav, [role="navigation"]');
    if (!hasNav) {
      this.addIssue({
        severity: 'info',
        wcagRule: '1.3.1',
        element: 'page',
        description: 'Page missing navigation landmark',
        suggestion: 'Add a nav element or role="navigation" for main navigation'
      });
    }
  }

  /**
   * Check color contrast (simplified check)
   */
  private checkColorContrast(container: HTMLElement): void {
    // This is a simplified check - in production, you'd use a proper color contrast library
    const textElements = container.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
    
    textElements.forEach((element, index) => {
      const styles = window.getComputedStyle(element);
      const color = styles.color;
      const backgroundColor = styles.backgroundColor;
      
      // Simple check for very light text on light backgrounds
      if (color.includes('rgb(255') && backgroundColor.includes('rgb(255')) {
        this.addIssue({
          severity: 'warning',
          wcagRule: '1.4.3',
          element: `${element.tagName.toLowerCase()}[${index}]`,
          description: 'Potential color contrast issue',
          suggestion: 'Verify color contrast meets 4.5:1 ratio requirement'
        });
      }
    });
  }

  /**
   * Add an issue to the audit results
   */
  private addIssue(issue: AccessibilityIssue): void {
    this.issues.push(issue);
  }

  /**
   * Calculate accessibility score
   */
  private calculateScore(): number {
    const totalChecks = 20; // Approximate number of checks performed
    const errors = this.issues.filter(issue => issue.severity === 'error').length;
    const warnings = this.issues.filter(issue => issue.severity === 'warning').length;
    
    const deductions = (errors * 10) + (warnings * 5);
    return Math.max(0, 100 - deductions);
  }

  /**
   * Generate recommendations based on issues found
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.issues.some(issue => issue.wcagRule === '1.1.1')) {
      recommendations.push('Add alt text to all images for screen reader users');
    }
    
    if (this.issues.some(issue => issue.wcagRule === '4.1.2')) {
      recommendations.push('Ensure all interactive elements have accessible names');
    }
    
    if (this.issues.some(issue => issue.wcagRule === '2.5.5')) {
      recommendations.push('Increase touch target sizes to at least 44x44 pixels');
    }
    
    if (this.issues.some(issue => issue.wcagRule === '1.4.3')) {
      recommendations.push('Verify color contrast meets WCAG AA requirements');
    }
    
    return recommendations;
  }
}

/**
 * Quick accessibility check for development
 */
export function quickAccessibilityCheck(): void {
  if (process.env.NODE_ENV === 'development') {
    const auditor = new AccessibilityAuditor();
    const results = auditor.auditPage();
    
    if (results.issues.length > 0) {
      console.group('🔍 Accessibility Audit Results');
      console.log(`Score: ${results.score}/100`);
      console.log(`Issues found: ${results.issues.length}`);
      
      results.issues.forEach(issue => {
        const emoji = issue.severity === 'error' ? '❌' : issue.severity === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`${emoji} ${issue.description} (WCAG ${issue.wcagRule})`);
        console.log(`   Suggestion: ${issue.suggestion}`);
      });
      
      if (results.recommendations.length > 0) {
        console.log('\n📋 Recommendations:');
        results.recommendations.forEach(rec => console.log(`• ${rec}`));
      }
      
      console.groupEnd();
    } else {
      console.log('✅ No accessibility issues found!');
    }
  }
}
