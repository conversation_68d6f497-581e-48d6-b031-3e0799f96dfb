'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  getCurrentBreakpoint, 
  breakpoints, 
  containerClasses,
  accessibility,
  grid
} from '@/lib/responsive-utils';
import { AccessibilityAuditor } from '@/lib/accessibility-audit';
import { Monitor, Smartphone, Tablet, CheckCircle, AlertTriangle, Info } from 'lucide-react';

/**
 * ResponsiveTestComponent - Development tool for testing responsive design
 * and accessibility compliance across all service pages.
 * 
 * This component should only be used in development mode.
 */
export function ResponsiveTestComponent() {
  const [currentBreakpoint, setCurrentBreakpoint] = useState<string>('desktop');
  const [windowSize, setWindowSize] = useState({ width: 0, height: 0 });
  const [auditResults, setAuditResults] = useState<any>(null);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateBreakpoint = () => {
      setCurrentBreakpoint(getCurrentBreakpoint());
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  const runAccessibilityAudit = () => {
    const auditor = new AccessibilityAuditor();
    const results = auditor.auditPage();
    setAuditResults(results);
  };

  const getBreakpointIcon = (breakpoint: string) => {
    switch (breakpoint) {
      case 'mobile': return <Smartphone className="w-4 h-4" />;
      case 'tablet': return <Tablet className="w-4 h-4" />;
      default: return <Monitor className="w-4 h-4" />;
    }
  };

  const getBreakpointColor = (breakpoint: string) => {
    switch (breakpoint) {
      case 'mobile': return 'bg-red-100 text-red-800 border-red-200';
      case 'tablet': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      <Card className="shadow-lg border-2 border-blue-200 bg-white/95 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-semibold flex items-center gap-2">
            <Monitor className="w-4 h-4" />
            Responsive Design Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Breakpoint */}
          <div className="space-y-2">
            <div className="text-xs font-medium text-gray-600">Current Breakpoint</div>
            <Badge className={`${getBreakpointColor(currentBreakpoint)} flex items-center gap-1`}>
              {getBreakpointIcon(currentBreakpoint)}
              {currentBreakpoint.toUpperCase()}
            </Badge>
            <div className="text-xs text-gray-500">
              {windowSize.width} × {windowSize.height}px
            </div>
          </div>

          {/* Breakpoint Ranges */}
          <div className="space-y-2">
            <div className="text-xs font-medium text-gray-600">Breakpoint Ranges</div>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span>Mobile:</span>
                <span>{breakpoints.mobile.min}-{breakpoints.mobile.max}px</span>
              </div>
              <div className="flex justify-between">
                <span>Tablet:</span>
                <span>{breakpoints.tablet.min}-{breakpoints.tablet.max}px</span>
              </div>
              <div className="flex justify-between">
                <span>Desktop:</span>
                <span>{breakpoints.desktop.min}px+</span>
              </div>
            </div>
          </div>

          {/* Container Classes Test */}
          <div className="space-y-2">
            <div className="text-xs font-medium text-gray-600">Container Classes</div>
            <div className="space-y-1">
              <div className="text-xs p-2 bg-gray-50 rounded border">
                <code className="text-xs">{containerClasses.base}</code>
              </div>
            </div>
          </div>

          {/* Grid System Test */}
          <div className="space-y-2">
            <div className="text-xs font-medium text-gray-600">Grid System</div>
            <div className="grid grid-cols-3 gap-1">
              <div className="h-4 bg-blue-200 rounded"></div>
              <div className="h-4 bg-blue-200 rounded"></div>
              <div className="h-4 bg-blue-200 rounded"></div>
            </div>
            <div className="text-xs text-gray-500">
              Cards: {grid.cards.mobile} {grid.cards.tablet} {grid.cards.desktop}
            </div>
          </div>

          {/* Accessibility Audit */}
          <div className="space-y-2">
            <div className="text-xs font-medium text-gray-600">Accessibility Audit</div>
            <Button
              size="sm"
              onClick={runAccessibilityAudit}
              className="w-full text-xs h-8"
            >
              Run A11y Audit
            </Button>
            
            {auditResults && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-xs">Score:</span>
                  <Badge variant={auditResults.score >= 90 ? 'default' : 'destructive'}>
                    {auditResults.score}/100
                  </Badge>
                </div>
                
                {auditResults.issues.length > 0 && (
                  <div className="space-y-1">
                    <div className="text-xs font-medium">Issues Found:</div>
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {auditResults.issues.slice(0, 3).map((issue: any, index: number) => (
                        <div key={index} className="text-xs p-2 bg-gray-50 rounded border">
                          <div className="flex items-center gap-1 mb-1">
                            {issue.severity === 'error' && <AlertTriangle className="w-3 h-3 text-red-500" />}
                            {issue.severity === 'warning' && <AlertTriangle className="w-3 h-3 text-yellow-500" />}
                            {issue.severity === 'info' && <Info className="w-3 h-3 text-blue-500" />}
                            <span className="font-medium">{issue.severity}</span>
                          </div>
                          <div>{issue.description}</div>
                          <div className="text-gray-500 mt-1">WCAG {issue.wcagRule}</div>
                        </div>
                      ))}
                      {auditResults.issues.length > 3 && (
                        <div className="text-xs text-gray-500 text-center">
                          +{auditResults.issues.length - 3} more issues
                        </div>
                      )}
                    </div>
                  </div>
                )}
                
                {auditResults.issues.length === 0 && (
                  <div className="flex items-center gap-1 text-xs text-green-600">
                    <CheckCircle className="w-3 h-3" />
                    No issues found!
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Touch Target Test */}
          <div className="space-y-2">
            <div className="text-xs font-medium text-gray-600">Touch Target Test</div>
            <div className="flex gap-2">
              <div className="w-11 h-11 bg-green-200 rounded border-2 border-green-400 flex items-center justify-center text-xs">
                44px
              </div>
              <div className="w-8 h-8 bg-red-200 rounded border-2 border-red-400 flex items-center justify-center text-xs">
                32px
              </div>
            </div>
            <div className="text-xs text-gray-500">
              Green: WCAG compliant (44px+)<br />
              Red: Too small (&lt;44px)
            </div>
          </div>

          {/* Focus Test */}
          <div className="space-y-2">
            <div className="text-xs font-medium text-gray-600">Focus Test</div>
            <Button
              size="sm"
              className={`w-full text-xs h-8 ${accessibility.focusRing}`}
            >
              Test Focus Ring
            </Button>
            <div className="text-xs text-gray-500">
              Tab to test keyboard navigation
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Hook to conditionally render responsive test component
 */
export function useResponsiveTest() {
  const [showTest, setShowTest] = useState(false);

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      // Toggle with Ctrl+Shift+R
      if (e.ctrlKey && e.shiftKey && e.key === 'R') {
        setShowTest(prev => !prev);
      }
    };

    if (process.env.NODE_ENV === 'development') {
      window.addEventListener('keydown', handleKeyPress);
      return () => window.removeEventListener('keydown', handleKeyPress);
    }
  }, []);

  return { showTest, ResponsiveTestComponent };
}
