'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Home, 
  Shield, 
  Camera, 
  CheckCircle, 
  Bell, 
  Lock,
  Users,
  Zap,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ServiceIndicatorProps {
  icon: React.ReactNode;
  value: string;
  label: string;
  status?: 'smart' | 'secure' | 'monitored';
}

function ServiceIndicator({ icon, value, label, status = 'smart' }: ServiceIndicatorProps) {
  const statusColors = {
    smart: 'text-purple-600 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-950 dark:border-purple-800',
    secure: 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-950 dark:border-green-800',
    monitored: 'text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-950 dark:border-amber-800'
  };

  return (
    <div className="flex items-center space-x-3 text-center sm:text-left">
      <div className={cn(
        "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center border",
        statusColors[status]
      )}>
        {icon}
      </div>
      <div className="min-w-0 flex-1">
        <div className="text-xl font-bold text-foreground">{value}</div>
        <div className="text-sm text-muted-foreground leading-tight">{label}</div>
      </div>
    </div>
  );
}

export function AdditionalServicesHeroSection() {
  const t = useTranslations('additionalServices');
  const tCta = useTranslations('cta');
  const locale = useLocale();

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-purple-50/30 dark:to-purple-950/20">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-24">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Content Column */}
          <div className="space-y-8 order-2 lg:order-1">
            {/* Service Badge */}
            <div className="flex justify-center lg:justify-start">
              <Badge variant="secondary" className="px-4 py-2 text-sm font-medium bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800">
                <Home className="w-4 h-4 mr-2" />
                {t('badge')}
              </Badge>
            </div>

            {/* Main Headline */}
            <div className="space-y-4 text-center lg:text-left">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-foreground leading-tight">
                <span className="text-purple-600">{t('hero.title.highlight')}</span>
                <br />
                {t('hero.title.main')}
              </h1>

              <p className="text-xl text-muted-foreground font-medium leading-relaxed">
                {t('hero.subtitle')}
              </p>

              <p className="text-lg text-muted-foreground max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                {t('hero.description')}
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button size="lg" className="text-base px-8 py-6 h-auto min-h-[48px] font-medium bg-purple-600 hover:bg-purple-700" asChild>
                <Link href={`/${locale}/schedule-consultation`}>
                  <Shield className="w-5 h-5 mr-2" />
                  {tCta('scheduleConsultation')}
                </Link>
              </Button>

              <Button variant="outline" size="lg" className="text-base px-8 py-6 h-auto min-h-[48px] font-medium border-purple-200 text-purple-700 hover:bg-purple-50" asChild>
                <Link href={`/${locale}/services`}>
                  {tCta('learnMore')}
                </Link>
              </Button>
            </div>

            {/* Service Indicators */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8 border-t border-border">
              <ServiceIndicator
                icon={<Home className="w-5 h-5" />}
                value={t('indicators.facilities')}
                label={t('indicators.facilitiesLabel')}
                status="smart"
              />
              <ServiceIndicator
                icon={<Camera className="w-5 h-5" />}
                value={t('indicators.surveillance')}
                label={t('indicators.surveillanceLabel')}
                status="monitored"
              />
              <ServiceIndicator
                icon={<Bell className="w-5 h-5" />}
                value={t('indicators.alarms')}
                label={t('indicators.alarmsLabel')}
                status="secure"
              />
            </div>
          </div>

          {/* Visual Column */}
          <div className="relative order-1 lg:order-2">
            {/* Smart Care Facility Mockup */}
            <div className="relative aspect-[4/3] rounded-2xl overflow-hidden bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 border border-purple-200 dark:border-purple-800">
              <div className="absolute inset-0 flex items-center justify-center p-6">
                <div className="text-center space-y-4 w-full">
                  <div className="w-20 h-20 bg-purple-600 rounded-full flex items-center justify-center mx-auto">
                    <Home className="w-10 h-10 text-white" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold text-foreground">{t('visual.title')}</h3>
                    <p className="text-base text-muted-foreground">{t('visual.subtitle')}</p>
                  </div>
                  
                  {/* Mock Service Cards */}
                  <div className="grid grid-cols-2 gap-3 mt-6">
                    <Card className="p-3 bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800">
                      <CardContent className="p-0 text-center">
                        <Camera className="w-6 h-6 text-green-600 mx-auto mb-1" />
                        <div className="text-xs font-medium text-green-700 dark:text-green-300">Überwachung</div>
                      </CardContent>
                    </Card>
                    <Card className="p-3 bg-amber-50 border-amber-200 dark:bg-amber-950 dark:border-amber-800">
                      <CardContent className="p-0 text-center">
                        <Bell className="w-6 h-6 text-amber-600 mx-auto mb-1" />
                        <div className="text-xs font-medium text-amber-700 dark:text-amber-300">Alarme</div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>

              {/* Floating Status Elements */}
              <div className="absolute top-4 right-4">
                <Card className="p-2 shadow-lg bg-white/90 dark:bg-gray-900/90">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-xs font-medium">{t('visual.statusActive')}</span>
                  </CardContent>
                </Card>
              </div>

              <div className="absolute bottom-4 left-4">
                <Card className="p-2 shadow-lg bg-white/90 dark:bg-gray-900/90">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <Lock className="w-3 h-3 text-purple-600" />
                    <span className="text-xs font-medium">{t('visual.statusSecure')}</span>
                  </CardContent>
                </Card>
              </div>

              <div className="absolute top-4 left-4">
                <Card className="p-2 shadow-lg bg-white/90 dark:bg-gray-900/90">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <Zap className="w-3 h-3 text-purple-600" />
                    <span className="text-xs font-medium">{t('visual.statusSmart')}</span>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Background Decorations */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-purple-500/10 rounded-full blur-xl" />
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-purple-600/10 rounded-full blur-xl" />
          </div>
        </div>

        {/* Service Categories Preview */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 rounded-2xl p-8 border border-purple-200 dark:border-purple-800">
            <div className="max-w-3xl mx-auto">
              <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
                {t('preview.title')}
              </h3>
              <p className="text-lg text-muted-foreground mb-6">
                {t('preview.description')}
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground justify-center md:justify-start">
                  <Home className="w-4 h-4 text-purple-600" />
                  <span>{t('preview.service1')}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground justify-center md:justify-start">
                  <Camera className="w-4 h-4 text-purple-600" />
                  <span>{t('preview.service2')}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground justify-center md:justify-start">
                  <Bell className="w-4 h-4 text-purple-600" />
                  <span>{t('preview.service3')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
