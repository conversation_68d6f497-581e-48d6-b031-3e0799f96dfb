'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Shield, 
  CheckCircle, 
  ArrowRight, 
  Phone, 
  Mail, 
  Calendar,
  Award,
  Users,
  Clock
} from 'lucide-react';

interface StatCardProps {
  icon: React.ReactNode;
  value: string;
  label: string;
  description: string;
}

function StatCard({ icon, value, label, description }: StatCardProps) {
  return (
    <Card className="text-center p-6 bg-white/50 dark:bg-gray-900/50 border-blue-200 dark:border-blue-800">
      <CardContent className="p-0 space-y-3">
        <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto">
          {icon}
        </div>
        <div className="space-y-1">
          <div className="text-2xl font-bold text-foreground">{value}</div>
          <div className="text-sm font-medium text-blue-600 dark:text-blue-400">{label}</div>
          <div className="text-xs text-muted-foreground">{description}</div>
        </div>
      </CardContent>
    </Card>
  );
}

export function IoMTCTASection() {
  const t = useTranslations('iomt');
  const tCta = useTranslations('cta');
  const locale = useLocale();

  const stats = [
    {
      icon: <Shield className="w-6 h-6 text-blue-600 dark:text-blue-400" />,
      value: t('cta.stats.devices'),
      label: t('cta.stats.devicesLabel'),
      description: t('cta.stats.devicesDescription')
    },
    {
      icon: <Users className="w-6 h-6 text-blue-600 dark:text-blue-400" />,
      value: t('cta.stats.facilities'),
      label: t('cta.stats.facilitiesLabel'),
      description: t('cta.stats.facilitiesDescription')
    },
    {
      icon: <Award className="w-6 h-6 text-blue-600 dark:text-blue-400" />,
      value: t('cta.stats.compliance'),
      label: t('cta.stats.complianceLabel'),
      description: t('cta.stats.complianceDescription')
    },
    {
      icon: <Clock className="w-6 h-6 text-blue-600 dark:text-blue-400" />,
      value: t('cta.stats.response'),
      label: t('cta.stats.responseLabel'),
      description: t('cta.stats.responseDescription')
    }
  ];

  const benefits = [
    t('cta.benefits.0'),
    t('cta.benefits.1'),
    t('cta.benefits.2'),
    t('cta.benefits.3'),
    t('cta.benefits.4'),
    t('cta.benefits.5')
  ];

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-blue-950 dark:via-background dark:to-blue-950">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Stats Section */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-16">
          {stats.map((stat, index) => (
            <StatCard
              key={index}
              icon={stat.icon}
              value={stat.value}
              label={stat.label}
              description={stat.description}
            />
          ))}
        </div>

        {/* Main CTA */}
        <div className="max-w-4xl mx-auto">
          <Card className="overflow-hidden bg-gradient-to-r from-blue-600 to-blue-700 border-0 text-white">
            <CardContent className="p-8 sm:p-12 lg:p-16">
              <div className="text-center space-y-8">
                {/* Header */}
                <div className="space-y-4">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                  
                  <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white">
                    {t('cta.title')}
                  </h2>
                  
                  <p className="text-xl text-blue-100 max-w-2xl mx-auto leading-relaxed">
                    {t('cta.description')}
                  </p>
                </div>

                {/* Benefits Grid */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 text-left">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-300 mt-0.5 flex-shrink-0" />
                      <span className="text-blue-100 text-sm">{benefit}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
                  <Button 
                    size="lg" 
                    className="text-base px-8 py-6 h-auto min-h-[48px] font-medium bg-white text-blue-600 hover:bg-blue-50 border-0"
                    asChild
                  >
                    <Link href={`/${locale}/schedule-consultation`}>
                      <Calendar className="w-5 h-5 mr-2" />
                      {tCta('scheduleConsultation')}
                    </Link>
                  </Button>

                  <Button 
                    variant="outline" 
                    size="lg" 
                    className="text-base px-8 py-6 h-auto min-h-[48px] font-medium border-white/30 text-white hover:bg-white/10"
                    asChild
                  >
                    <Link href={`/${locale}/services`}>
                      {t('cta.viewAllServices')}
                      <ArrowRight className="w-5 h-5 ml-2" />
                    </Link>
                  </Button>
                </div>

                {/* Contact Options */}
                <div className="pt-8 border-t border-white/20">
                  <p className="text-blue-100 mb-4">{t('cta.contactText')}</p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <div className="flex items-center space-x-2 text-blue-100">
                      <Phone className="w-4 h-4" />
                      <span className="text-sm">{t('cta.phone')}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-blue-100">
                      <Mail className="w-4 h-4" />
                      <span className="text-sm">{t('cta.email')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Information */}
        <div className="text-center mt-12 max-w-2xl mx-auto">
          <p className="text-muted-foreground text-sm leading-relaxed">
            {t('cta.additionalInfo')}
          </p>
        </div>
      </div>
    </section>
  );
}
