'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Monitor, 
  Heart, 
  Activity, 
  Smartphone, 
  Cloud, 
  Shield,
  CheckCircle,
  Users,
  BarChart3,
  Bell,
  FileText,
  Zap
} from 'lucide-react';

interface PlatformFeatureProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  capabilities: string[];
  badge?: string;
  priority?: 'core' | 'advanced' | 'premium';
}

function PlatformFeature({ icon, title, description, capabilities, badge, priority = 'core' }: PlatformFeatureProps) {
  const priorityStyles = {
    core: 'border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50',
    advanced: 'border-purple-200 bg-purple-50/50 dark:border-purple-800 dark:bg-purple-950/50',
    premium: 'border-amber-200 bg-amber-50/50 dark:border-amber-800 dark:bg-amber-950/50'
  };

  const iconStyles = {
    core: 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900',
    advanced: 'text-purple-600 bg-purple-100 dark:text-purple-400 dark:bg-purple-900',
    premium: 'text-amber-600 bg-amber-100 dark:text-amber-400 dark:bg-amber-900'
  };

  return (
    <Card className={`h-full transition-all duration-200 hover:shadow-lg ${priorityStyles[priority]}`}>
      <CardHeader className="space-y-4">
        <div className="flex items-start justify-between">
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${iconStyles[priority]}`}>
            {icon}
          </div>
          {badge && (
            <Badge variant="secondary" className="text-xs">
              {badge}
            </Badge>
          )}
        </div>
        <CardTitle className="text-xl font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-muted-foreground leading-relaxed">{description}</p>
        <ul className="space-y-2">
          {capabilities.map((capability, index) => (
            <li key={index} className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-muted-foreground">{capability}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}

export function TelemonitoringPlatformSection() {
  const t = useTranslations('telemonitoring');

  const platformFeatures = [
    {
      icon: <Heart className="w-6 h-6" />,
      title: t('platform.vitals.title'),
      description: t('platform.vitals.description'),
      capabilities: [
        t('platform.vitals.capabilities.0'),
        t('platform.vitals.capabilities.1'),
        t('platform.vitals.capabilities.2'),
        t('platform.vitals.capabilities.3')
      ],
      badge: t('platform.vitals.badge'),
      priority: 'core' as const
    },
    {
      icon: <Bell className="w-6 h-6" />,
      title: t('platform.alerts.title'),
      description: t('platform.alerts.description'),
      capabilities: [
        t('platform.alerts.capabilities.0'),
        t('platform.alerts.capabilities.1'),
        t('platform.alerts.capabilities.2'),
        t('platform.alerts.capabilities.3')
      ],
      badge: t('platform.alerts.badge'),
      priority: 'core' as const
    },
    {
      icon: <BarChart3 className="w-6 h-6" />,
      title: t('platform.analytics.title'),
      description: t('platform.analytics.description'),
      capabilities: [
        t('platform.analytics.capabilities.0'),
        t('platform.analytics.capabilities.1'),
        t('platform.analytics.capabilities.2'),
        t('platform.analytics.capabilities.3')
      ],
      priority: 'advanced' as const
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: t('platform.management.title'),
      description: t('platform.management.description'),
      capabilities: [
        t('platform.management.capabilities.0'),
        t('platform.management.capabilities.1'),
        t('platform.management.capabilities.2'),
        t('platform.management.capabilities.3')
      ],
      priority: 'advanced' as const
    },
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: t('platform.mobile.title'),
      description: t('platform.mobile.description'),
      capabilities: [
        t('platform.mobile.capabilities.0'),
        t('platform.mobile.capabilities.1'),
        t('platform.mobile.capabilities.2'),
        t('platform.mobile.capabilities.3')
      ],
      badge: t('platform.mobile.badge'),
      priority: 'core' as const
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: t('platform.documentation.title'),
      description: t('platform.documentation.description'),
      capabilities: [
        t('platform.documentation.capabilities.0'),
        t('platform.documentation.capabilities.1'),
        t('platform.documentation.capabilities.2'),
        t('platform.documentation.capabilities.3')
      ],
      priority: 'premium' as const
    }
  ];

  const architectureFeatures = [
    {
      icon: <Cloud className="w-6 h-6 text-blue-600" />,
      title: t('platform.architecture.cloud.title'),
      description: t('platform.architecture.cloud.description')
    },
    {
      icon: <Shield className="w-6 h-6 text-green-600" />,
      title: t('platform.architecture.security.title'),
      description: t('platform.architecture.security.description')
    },
    {
      icon: <Zap className="w-6 h-6 text-purple-600" />,
      title: t('platform.architecture.performance.title'),
      description: t('platform.architecture.performance.description')
    },
    {
      icon: <Activity className="w-6 h-6 text-amber-600" />,
      title: t('platform.architecture.integration.title'),
      description: t('platform.architecture.integration.description')
    }
  ];

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-12 sm:mb-16">
          <Badge variant="secondary" className="mb-4 px-3 py-1 text-sm bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800">
            <Monitor className="w-4 h-4 mr-2" />
            {t('platform.badge')}
          </Badge>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            {t('platform.title')}
          </h2>
          
          <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed">
            {t('platform.description')}
          </p>
        </div>

        {/* Platform Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mb-16">
          {platformFeatures.map((feature, index) => (
            <PlatformFeature
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              capabilities={feature.capabilities}
              badge={feature.badge}
              priority={feature.priority}
            />
          ))}
        </div>

        {/* Architecture Section */}
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-2xl p-8 sm:p-12 border border-blue-200 dark:border-blue-800">
          <div className="text-center mb-8">
            <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
              {t('platform.architecture.title')}
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {t('platform.architecture.description')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {architectureFeatures.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-white dark:bg-gray-900 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-sm">
                  {feature.icon}
                </div>
                <h4 className="font-semibold text-foreground mb-2">{feature.title}</h4>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Integration Capabilities */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-8">
            {t('platform.integration.title')}
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <div
                key={index}
                className="h-16 bg-muted rounded-lg flex items-center justify-center px-2 border border-border hover:border-blue-200 transition-colors"
              >
                <span className="text-muted-foreground text-xs font-medium text-center">
                  {t(`platform.integration.partner${index + 1}`)}
                </span>
              </div>
            ))}
          </div>
          
          <p className="text-muted-foreground mt-6 max-w-2xl mx-auto">
            {t('platform.integration.description')}
          </p>
        </div>
      </div>
    </section>
  );
}
