'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Award, 
  Star, 
  Quote, 
  Shield, 
  CheckCircle,
  Building2,
  Users,
  TrendingUp
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TestimonialProps {
  name: string;
  title: string;
  company: string;
  content: string;
  rating: number;
  avatar?: string;
  facilityType: string;
}

function TestimonialCard({ name, title, company, content, rating, avatar, facilityType }: TestimonialProps) {
  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3 sm:pb-4 flex-shrink-0">
        <div className="flex items-start space-x-3 sm:space-x-4">
          <Avatar className="w-10 h-10 sm:w-12 sm:h-12 flex-shrink-0">
            <AvatarImage src={avatar} alt={name} />
            <AvatarFallback className="bg-primary/10 text-primary font-semibold text-sm">
              {name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-2 mb-1">
              <h4 className="font-semibold text-foreground text-sm sm:text-base leading-tight">{name}</h4>
              <Badge variant="secondary" className="text-xs px-2 py-0.5 mt-1 sm:mt-0 self-start">
                {facilityType}
              </Badge>
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground leading-tight">{title}</p>
            <p className="text-xs sm:text-sm text-muted-foreground font-medium leading-tight">{company}</p>
            <div className="flex items-center mt-1.5 sm:mt-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <Star
                  key={i}
                  className={cn(
                    "w-3 h-3 sm:w-4 sm:h-4",
                    i < rating ? "text-yellow-400 fill-current" : "text-muted-foreground"
                  )}
                />
              ))}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-1">
        <div className="relative">
          <Quote className="w-4 h-4 sm:w-6 sm:h-6 text-muted-foreground/30 absolute -top-1 sm:-top-2 -left-1" />
          <p className="text-muted-foreground italic pl-3 sm:pl-4 text-sm sm:text-base leading-relaxed">
            {content}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

interface CertificationProps {
  name: string;
  description: string;
  icon: React.ReactNode;
  validUntil?: string;
  prominent?: boolean;
}

function CertificationBadge({ name, description, icon, validUntil, prominent }: CertificationProps) {
  return (
    <Card className={cn(
      "text-center transition-all duration-300 hover:shadow-lg hover:-translate-y-1 h-full",
      prominent && "ring-2 ring-accent/20 bg-accent/5"
    )}>
      <CardContent className="p-4 sm:p-6 h-full flex flex-col">
        <div className={cn(
          "w-12 h-12 sm:w-16 sm:h-16 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 flex-shrink-0",
          prominent ? "bg-accent/20" : "bg-primary/10"
        )}>
          {icon}
        </div>
        <h3 className={cn(
          "font-semibold mb-2 text-sm sm:text-base leading-tight",
          prominent ? "text-accent" : "text-foreground"
        )}>
          {name}
        </h3>
        <p className="text-xs sm:text-sm text-muted-foreground mb-2 leading-relaxed flex-1">{description}</p>
        {validUntil && (
          <p className="text-xs text-muted-foreground mt-auto">Valid until {validUntil}</p>
        )}
      </CardContent>
    </Card>
  );
}

export function TrustSocialProofSection() {
  const testimonials: TestimonialProps[] = [
    {
      name: "Dr. Maria Schmidt",
      title: "Leitende Ärztin",
      company: "Kardiologische Praxis München",
      content: "Die GBA-Konformität von medPower® gab uns das Vertrauen in die Plattform. Seit der Implementierung haben wir 60% weniger Notfalleinweisungen und unsere Patienten fühlen sich deutlich sicherer betreut.",
      rating: 5,
      facilityType: "Arztpraxis"
    },
    {
      name: "Thomas Weber",
      title: "IT-Leiter",
      company: "Gesundheitszentrum Berlin",
      content: "Die nahtlose Integration in unsere bestehenden Systeme war beeindruckend. Die cloudbasierte Technologie funktioniert einwandfrei und die Schulung des Personals war minimal aufgrund der intuitiven Benutzeroberfläche.",
      rating: 5,
      facilityType: "Gesundheitszentrum"
    },
    {
      name: "Schwester Anna Müller",
      title: "Pflegedienstleitung",
      company: "Chroniker-Betreuung Hamburg",
      content: "Als kleinere Einrichtung brauchten wir eine Lösung, die sowohl leistungsstark als auch kosteneffektiv ist. AITELMED hat genau das geliefert. Das System hat unsere Patientenbetreuung revolutioniert.",
      rating: 5,
      facilityType: "Pflegedienst"
    }
  ];

  const certifications: CertificationProps[] = [
    {
      name: "GBA-konform",
      description: "Zertifizierte Telemedizin-Plattform nach deutschen Gesundheitsstandards",
      icon: <Award className="w-8 h-8 text-accent" />,
      validUntil: "2026",
      prominent: true
    },
    {
      name: "Medizinprodukt Klasse IIa",
      description: "Zertifizierung als Medizinprodukt der Klasse IIa nach MDR",
      icon: <Shield className="w-8 h-8 text-primary" />,
      validUntil: "2025"
    },
    {
      name: "DSGVO-konform",
      description: "Vollständige Konformität mit der Datenschutz-Grundverordnung",
      icon: <CheckCircle className="w-8 h-8 text-primary" />,
      validUntil: "2027"
    },
    {
      name: "ISO 27001",
      description: "Informationssicherheits-Managementsystem Zertifizierung",
      icon: <Shield className="w-8 h-8 text-primary" />,
      validUntil: "2026"
    }
  ];

  const stats = [
    {
      icon: <Building2 className="w-6 h-6 text-primary" />,
      value: "200+",
      label: "Arztpraxen",
      description: "Vertrauen unseren Lösungen"
    },
    {
      icon: <Users className="w-6 h-6 text-primary" />,
      value: "5,000+",
      label: "Fernüberwachte Patienten",
      description: "Nutzen täglich unsere Plattform"
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-primary" />,
      value: "99.9%",
      label: "System-Verfügbarkeit",
      description: "Garantierte Zuverlässigkeit"
    },
    {
      icon: <Award className="w-6 h-6 text-primary" />,
      value: "10+",
      label: "Jahre Erfahrung",
      description: "In der Telemedizin"
    }
  ];

  return (
    <section className="py-12 sm:py-16 lg:py-20 xl:py-24 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center max-w-4xl mx-auto mb-12 sm:mb-16">
          <Badge variant="secondary" className="mb-3 sm:mb-4 px-3 py-1.5 text-xs sm:text-sm">
            Vertrauen & Zuverlässigkeit
          </Badge>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-4 sm:mb-6 leading-tight">
            Vertraut von Gesundheitsexperten deutschlandweit
          </h2>
          <p className="text-base sm:text-lg text-muted-foreground leading-relaxed">
            Unser Engagement für Qualität, Zuverlässigkeit und Compliance hat das Vertrauen von
            Gesundheitseinrichtungen in ganz Deutschland gewonnen.
          </p>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 mb-12 sm:mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="w-12 h-12 sm:w-16 sm:h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4">
                {stat.icon}
              </div>
              <div className="text-2xl sm:text-3xl font-bold text-foreground mb-1 leading-tight">{stat.value}</div>
              <div className="font-semibold text-foreground mb-1 text-sm sm:text-base leading-tight">{stat.label}</div>
              <div className="text-xs sm:text-sm text-muted-foreground leading-relaxed">{stat.description}</div>
            </div>
          ))}
        </div>

        {/* Certifications */}
        <div className="mb-12 sm:mb-16">
          <h3 className="text-xl sm:text-2xl font-semibold text-foreground text-center mb-6 sm:mb-8 leading-tight">
            Zertifizierungen & Compliance
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            {certifications.map((cert, index) => (
              <CertificationBadge key={index} {...cert} />
            ))}
          </div>
        </div>

        {/* Testimonials */}
        {/* <div className="mb-12 sm:mb-16">
          <h3 className="text-xl sm:text-2xl font-semibold text-foreground text-center mb-6 sm:mb-8 leading-tight">
            Was Gesundheitsexperten sagen
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard key={index} {...testimonial} />
            ))}
          </div>
        </div> */}

        {/* Client Logos */}
        {/* <div className="text-center">
          <h3 className="text-lg sm:text-xl font-semibold text-foreground mb-6 sm:mb-8 leading-tight">
            Vertraut von führenden Gesundheitseinrichtungen
          </h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6 lg:gap-8 items-center opacity-60">
            {Array.from({ length: 6 }).map((_, index) => (
              <div
                key={index}
                className="h-12 sm:h-16 bg-muted rounded-lg flex items-center justify-center hover:opacity-100 transition-opacity px-2"
              >
                <span className="text-muted-foreground text-xs sm:text-sm font-medium text-center">
                  Partner {index + 1}
                </span>
              </div>
            ))}
          </div>
        </div> */}
      </div>
    </section>
  );
}
