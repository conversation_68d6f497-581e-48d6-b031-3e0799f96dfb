'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Award, 
  FileCheck, 
  Lock, 
  CheckCircle, 
  AlertCircle,
  Globe,
  Building
} from 'lucide-react';

interface ComplianceCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  requirements: string[];
  status: 'certified' | 'compliant' | 'supported';
  region?: string;
}

function ComplianceCard({ icon, title, description, requirements, status, region }: ComplianceCardProps) {
  const statusConfig = {
    certified: {
      badge: 'Zertifiziert',
      badgeClass: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300 dark:border-green-800',
      iconClass: 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900',
      borderClass: 'border-green-200 bg-green-50/30 dark:border-green-800 dark:bg-green-950/30'
    },
    compliant: {
      badge: 'Konform',
      badgeClass: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-800',
      iconClass: 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900',
      borderClass: 'border-blue-200 bg-blue-50/30 dark:border-blue-800 dark:bg-blue-950/30'
    },
    supported: {
      badge: 'Unterstützt',
      badgeClass: 'bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900 dark:text-amber-300 dark:border-amber-800',
      iconClass: 'text-amber-600 bg-amber-100 dark:text-amber-400 dark:bg-amber-900',
      borderClass: 'border-amber-200 bg-amber-50/30 dark:border-amber-800 dark:bg-amber-950/30'
    }
  };

  const config = statusConfig[status];

  return (
    <Card className={`h-full transition-all duration-200 hover:shadow-lg ${config.borderClass}`}>
      <CardHeader className="space-y-4">
        <div className="flex items-start justify-between">
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${config.iconClass}`}>
            {icon}
          </div>
          <div className="flex flex-col items-end space-y-2">
            <Badge className={config.badgeClass}>
              {config.badge}
            </Badge>
            {region && (
              <Badge variant="outline" className="text-xs">
                {region}
              </Badge>
            )}
          </div>
        </div>
        <CardTitle className="text-xl font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-muted-foreground leading-relaxed">{description}</p>
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-foreground">Wichtige Anforderungen:</h4>
          <ul className="space-y-2">
            {requirements.map((requirement, index) => (
              <li key={index} className="flex items-start space-x-2">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                <span className="text-sm text-muted-foreground">{requirement}</span>
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

export function IoMTComplianceSection() {
  const t = useTranslations('iomt');

  const complianceStandards = [
    {
      icon: <Award className="w-6 h-6" />,
      title: t('compliance.gba.title'),
      description: t('compliance.gba.description'),
      requirements: [
        t('compliance.gba.requirements.0'),
        t('compliance.gba.requirements.1'),
        t('compliance.gba.requirements.2'),
        t('compliance.gba.requirements.3')
      ],
      status: 'certified' as const,
      region: 'Deutschland'
    },
    {
      icon: <Lock className="w-6 h-6" />,
      title: t('compliance.gdpr.title'),
      description: t('compliance.gdpr.description'),
      requirements: [
        t('compliance.gdpr.requirements.0'),
        t('compliance.gdpr.requirements.1'),
        t('compliance.gdpr.requirements.2'),
        t('compliance.gdpr.requirements.3')
      ],
      status: 'compliant' as const,
      region: 'EU'
    },
    {
      icon: <FileCheck className="w-6 h-6" />,
      title: t('compliance.mdr.title'),
      description: t('compliance.mdr.description'),
      requirements: [
        t('compliance.mdr.requirements.0'),
        t('compliance.mdr.requirements.1'),
        t('compliance.mdr.requirements.2'),
        t('compliance.mdr.requirements.3')
      ],
      status: 'certified' as const,
      region: 'EU'
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: t('compliance.iso27001.title'),
      description: t('compliance.iso27001.description'),
      requirements: [
        t('compliance.iso27001.requirements.0'),
        t('compliance.iso27001.requirements.1'),
        t('compliance.iso27001.requirements.2'),
        t('compliance.iso27001.requirements.3')
      ],
      status: 'compliant' as const,
      region: 'International'
    },
    {
      icon: <Building className="w-6 h-6" />,
      title: t('compliance.hipaa.title'),
      description: t('compliance.hipaa.description'),
      requirements: [
        t('compliance.hipaa.requirements.0'),
        t('compliance.hipaa.requirements.1'),
        t('compliance.hipaa.requirements.2'),
        t('compliance.hipaa.requirements.3')
      ],
      status: 'supported' as const,
      region: 'USA'
    },
    {
      icon: <Globe className="w-6 h-6" />,
      title: t('compliance.fda.title'),
      description: t('compliance.fda.description'),
      requirements: [
        t('compliance.fda.requirements.0'),
        t('compliance.fda.requirements.1'),
        t('compliance.fda.requirements.2'),
        t('compliance.fda.requirements.3')
      ],
      status: 'supported' as const,
      region: 'USA'
    }
  ];

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-12 sm:mb-16">
          <Badge variant="secondary" className="mb-4 px-3 py-1 text-sm bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800">
            <Award className="w-4 h-4 mr-2" />
            {t('compliance.badge')}
          </Badge>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            {t('compliance.title')}
          </h2>
          
          <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed">
            {t('compliance.description')}
          </p>
        </div>

        {/* Compliance Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mb-12">
          {complianceStandards.map((standard, index) => (
            <ComplianceCard
              key={index}
              icon={standard.icon}
              title={standard.title}
              description={standard.description}
              requirements={standard.requirements}
              status={standard.status}
              region={standard.region}
            />
          ))}
        </div>

        {/* Compliance Benefits */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950 dark:to-blue-950 rounded-2xl p-8 sm:p-12 border border-green-200 dark:border-green-800">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
                {t('compliance.benefits.title')}
              </h3>
              <p className="text-lg text-muted-foreground">
                {t('compliance.benefits.description')}
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
                </div>
                <h4 className="font-semibold text-foreground mb-2">{t('compliance.benefits.item1.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('compliance.benefits.item1.description')}</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Shield className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h4 className="font-semibold text-foreground mb-2">{t('compliance.benefits.item2.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('compliance.benefits.item2.description')}</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-amber-100 dark:bg-amber-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Award className="w-6 h-6 text-amber-600 dark:text-amber-400" />
                </div>
                <h4 className="font-semibold text-foreground mb-2">{t('compliance.benefits.item3.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('compliance.benefits.item3.description')}</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Lock className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h4 className="font-semibold text-foreground mb-2">{t('compliance.benefits.item4.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('compliance.benefits.item4.description')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
