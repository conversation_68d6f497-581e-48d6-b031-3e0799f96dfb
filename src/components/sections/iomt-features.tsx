'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Activity, 
  AlertTriangle, 
  Lock, 
  Eye, 
  Zap, 
  FileText, 
  Network,
  CheckCircle,
  Settings
} from 'lucide-react';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  features: string[];
  badge?: string;
  priority?: 'high' | 'medium' | 'standard';
}

function FeatureCard({ icon, title, description, features, badge, priority = 'standard' }: FeatureCardProps) {
  const priorityStyles = {
    high: 'border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50',
    medium: 'border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50',
    standard: 'border-border bg-card'
  };

  const iconStyles = {
    high: 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900',
    medium: 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900',
    standard: 'text-primary bg-primary/10'
  };

  return (
    <Card className={`h-full transition-all duration-200 hover:shadow-lg ${priorityStyles[priority]}`}>
      <CardHeader className="space-y-4">
        <div className="flex items-start justify-between">
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${iconStyles[priority]}`}>
            {icon}
          </div>
          {badge && (
            <Badge variant="secondary" className="text-xs">
              {badge}
            </Badge>
          )}
        </div>
        <CardTitle className="text-xl font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-muted-foreground leading-relaxed">{description}</p>
        <ul className="space-y-2">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-muted-foreground">{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}

export function IoMTFeaturesSection() {
  const t = useTranslations('iomt');

  const features = [
    {
      icon: <Eye className="w-6 h-6" />,
      title: t('features.visibility.title'),
      description: t('features.visibility.description'),
      features: [
        t('features.visibility.items.0'),
        t('features.visibility.items.1'),
        t('features.visibility.items.2'),
        t('features.visibility.items.3')
      ],
      badge: t('features.visibility.badge'),
      priority: 'high' as const
    },
    {
      icon: <AlertTriangle className="w-6 h-6" />,
      title: t('features.vulnerability.title'),
      description: t('features.vulnerability.description'),
      features: [
        t('features.vulnerability.items.0'),
        t('features.vulnerability.items.1'),
        t('features.vulnerability.items.2'),
        t('features.vulnerability.items.3')
      ],
      badge: t('features.vulnerability.badge'),
      priority: 'high' as const
    },
    {
      icon: <Network className="w-6 h-6" />,
      title: t('features.segmentation.title'),
      description: t('features.segmentation.description'),
      features: [
        t('features.segmentation.items.0'),
        t('features.segmentation.items.1'),
        t('features.segmentation.items.2'),
        t('features.segmentation.items.3')
      ],
      priority: 'medium' as const
    },
    {
      icon: <Activity className="w-6 h-6" />,
      title: t('features.monitoring.title'),
      description: t('features.monitoring.description'),
      features: [
        t('features.monitoring.items.0'),
        t('features.monitoring.items.1'),
        t('features.monitoring.items.2'),
        t('features.monitoring.items.3')
      ],
      priority: 'medium' as const
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: t('features.compliance.title'),
      description: t('features.compliance.description'),
      features: [
        t('features.compliance.items.0'),
        t('features.compliance.items.1'),
        t('features.compliance.items.2'),
        t('features.compliance.items.3')
      ],
      badge: t('features.compliance.badge'),
      priority: 'standard' as const
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: t('features.response.title'),
      description: t('features.response.description'),
      features: [
        t('features.response.items.0'),
        t('features.response.items.1'),
        t('features.response.items.2'),
        t('features.response.items.3')
      ],
      priority: 'standard' as const
    }
  ];

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-12 sm:mb-16">
          <Badge variant="secondary" className="mb-4 px-3 py-1 text-sm bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800">
            <Shield className="w-4 h-4 mr-2" />
            {t('features.badge')}
          </Badge>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            {t('features.title')}
          </h2>
          
          <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed">
            {t('features.description')}
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              features={feature.features}
              badge={feature.badge}
              priority={feature.priority}
            />
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-12 sm:mt-16">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-2xl p-8 sm:p-12 border border-blue-200 dark:border-blue-800">
            <div className="max-w-2xl mx-auto">
              <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
                {t('features.cta.title')}
              </h3>
              <p className="text-lg text-muted-foreground mb-6">
                {t('features.cta.description')}
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>{t('features.cta.benefit1')}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>{t('features.cta.benefit2')}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>{t('features.cta.benefit3')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
