'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Shield, Activity, Lock, CheckCircle, Award, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SecurityIndicatorProps {
  icon: React.ReactNode;
  value: string;
  label: string;
  status?: 'secure' | 'warning' | 'active';
}

function SecurityIndicator({ icon, value, label, status = 'secure' }: SecurityIndicatorProps) {
  const statusColors = {
    secure: 'text-green-600 bg-green-50 border-green-200',
    warning: 'text-amber-600 bg-amber-50 border-amber-200',
    active: 'text-blue-600 bg-blue-50 border-blue-200'
  };

  return (
    <div className="flex items-center space-x-3 text-center sm:text-left">
      <div className={cn(
        "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center border",
        statusColors[status]
      )}>
        {icon}
      </div>
      <div className="min-w-0 flex-1">
        <div className="text-xl font-bold text-foreground">{value}</div>
        <div className="text-sm text-muted-foreground leading-tight">{label}</div>
      </div>
    </div>
  );
}

export function IoMTHeroSection() {
  const t = useTranslations('iomt');
  const tCta = useTranslations('cta');
  const locale = useLocale();

  return (
    <section
      className="relative overflow-hidden bg-gradient-to-br from-background via-background to-blue-50/30 dark:to-blue-950/20"
      aria-labelledby="iomt-hero-title"
      role="banner"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" aria-hidden="true" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-24">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Content Column */}
          <div className="space-y-8 order-2 lg:order-1">
            {/* Service Badge */}
            <div className="flex justify-center lg:justify-start">
              <Badge variant="secondary" className="px-4 py-2 text-sm font-medium bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800">
                <Shield className="w-4 h-4 mr-2" />
                {t('badge')}
              </Badge>
            </div>

            {/* Main Headline */}
            <div className="space-y-4 text-center lg:text-left">
              <h1
                id="iomt-hero-title"
                className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-foreground leading-tight"
              >
                <span className="text-blue-600">{t('hero.title.highlight')}</span>
                <br />
                {t('hero.title.main')}
              </h1>

              <p className="text-xl text-muted-foreground font-medium leading-relaxed">
                {t('hero.subtitle')}
              </p>

              <p className="text-lg text-muted-foreground max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                {t('hero.description')}
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start" role="group" aria-label="Call to action buttons">
              <Button size="lg" className="text-base px-8 py-6 h-auto min-h-[48px] font-medium bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" asChild>
                <Link href={`/${locale}/schedule-consultation`} aria-describedby="iomt-hero-title">
                  <Shield className="w-5 h-5 mr-2" aria-hidden="true" />
                  {tCta('scheduleConsultation')}
                </Link>
              </Button>

              <Button variant="outline" size="lg" className="text-base px-8 py-6 h-auto min-h-[48px] font-medium border-blue-200 text-blue-700 hover:bg-blue-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" asChild>
                <Link href={`/${locale}/services`}>
                  {tCta('learnMore')}
                </Link>
              </Button>
            </div>

            {/* Security Indicators */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8 border-t border-border">
              <SecurityIndicator
                icon={<Shield className="w-5 h-5" />}
                value={t('indicators.devices')}
                label={t('indicators.devicesLabel')}
                status="secure"
              />
              <SecurityIndicator
                icon={<AlertTriangle className="w-5 h-5" />}
                value={t('indicators.threats')}
                label={t('indicators.threatsLabel')}
                status="warning"
              />
              <SecurityIndicator
                icon={<Activity className="w-5 h-5" />}
                value={t('indicators.monitoring')}
                label={t('indicators.monitoringLabel')}
                status="active"
              />
            </div>
          </div>

          {/* Visual Column */}
          <div className="relative order-1 lg:order-2">
            {/* IoMT Security Dashboard Mockup */}
            <div className="relative aspect-[4/3] rounded-2xl overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 border border-blue-200 dark:border-blue-800">
              <div className="absolute inset-0 flex items-center justify-center p-6">
                <div className="text-center space-y-4 w-full">
                  <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto">
                    <Shield className="w-10 h-10 text-white" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold text-foreground">{t('visual.title')}</h3>
                    <p className="text-base text-muted-foreground">{t('visual.subtitle')}</p>
                  </div>
                  
                  {/* Mock Security Status Cards */}
                  <div className="grid grid-cols-2 gap-3 mt-6">
                    <Card className="p-3 bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800">
                      <CardContent className="p-0 text-center">
                        <CheckCircle className="w-6 h-6 text-green-600 mx-auto mb-1" />
                        <div className="text-xs font-medium text-green-700 dark:text-green-300">Secure</div>
                      </CardContent>
                    </Card>
                    <Card className="p-3 bg-amber-50 border-amber-200 dark:bg-amber-950 dark:border-amber-800">
                      <CardContent className="p-0 text-center">
                        <AlertTriangle className="w-6 h-6 text-amber-600 mx-auto mb-1" />
                        <div className="text-xs font-medium text-amber-700 dark:text-amber-300">Monitoring</div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>

              {/* Floating Status Elements */}
              <div className="absolute top-4 right-4">
                <Card className="p-2 shadow-lg bg-white/90 dark:bg-gray-900/90">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-xs font-medium">{t('visual.statusSecure')}</span>
                  </CardContent>
                </Card>
              </div>

              <div className="absolute bottom-4 left-4">
                <Card className="p-2 shadow-lg bg-white/90 dark:bg-gray-900/90">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <Lock className="w-3 h-3 text-blue-600" />
                    <span className="text-xs font-medium">{t('visual.statusCompliant')}</span>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Background Decorations */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-500/10 rounded-full blur-xl" />
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-blue-600/10 rounded-full blur-xl" />
          </div>
        </div>
      </div>
    </section>
  );
}
