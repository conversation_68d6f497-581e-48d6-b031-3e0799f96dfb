'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Home, 
  Camera, 
  Bell, 
  Shield, 
  Zap, 
  Settings,
  CheckCircle,
  Users,
  Lock,
  Activity,
  AlertTriangle,
  Smartphone
} from 'lucide-react';

interface ServiceOfferProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  features: string[];
  technologies: string[];
  badge?: string;
  category: 'smart-home' | 'surveillance' | 'security';
}

function ServiceOffer({ icon, title, description, features, technologies, badge, category }: ServiceOfferProps) {
  const categoryStyles = {
    'smart-home': 'border-purple-200 bg-purple-50/50 dark:border-purple-800 dark:bg-purple-950/50',
    'surveillance': 'border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50',
    'security': 'border-amber-200 bg-amber-50/50 dark:border-amber-800 dark:bg-amber-950/50'
  };

  const iconStyles = {
    'smart-home': 'text-purple-600 bg-purple-100 dark:text-purple-400 dark:bg-purple-900',
    'surveillance': 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900',
    'security': 'text-amber-600 bg-amber-100 dark:text-amber-400 dark:bg-amber-900'
  };

  return (
    <Card className={`h-full transition-all duration-200 hover:shadow-lg ${categoryStyles[category]}`}>
      <CardHeader className="space-y-4">
        <div className="flex items-start justify-between">
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${iconStyles[category]}`}>
            {icon}
          </div>
          {badge && (
            <Badge variant="secondary" className="text-xs">
              {badge}
            </Badge>
          )}
        </div>
        <CardTitle className="text-xl font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <p className="text-muted-foreground leading-relaxed">{description}</p>
        
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-foreground mb-2">Hauptfunktionen:</h4>
            <ul className="space-y-2">
              {features.map((feature, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-muted-foreground">{feature}</span>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h4 className="text-sm font-medium text-foreground mb-2">Technologien:</h4>
            <div className="flex flex-wrap gap-2">
              {technologies.map((tech, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tech}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function AdditionalServicesOffersSection() {
  const t = useTranslations('additionalServices');

  const serviceOffers = [
    {
      icon: <Home className="w-6 h-6" />,
      title: t('offers.smartHome.title'),
      description: t('offers.smartHome.description'),
      features: [
        t('offers.smartHome.features.0'),
        t('offers.smartHome.features.1'),
        t('offers.smartHome.features.2'),
        t('offers.smartHome.features.3'),
        t('offers.smartHome.features.4')
      ],
      technologies: [
        t('offers.smartHome.technologies.0'),
        t('offers.smartHome.technologies.1'),
        t('offers.smartHome.technologies.2'),
        t('offers.smartHome.technologies.3')
      ],
      badge: t('offers.smartHome.badge'),
      category: 'smart-home' as const
    },
    {
      icon: <Camera className="w-6 h-6" />,
      title: t('offers.videoSurveillance.title'),
      description: t('offers.videoSurveillance.description'),
      features: [
        t('offers.videoSurveillance.features.0'),
        t('offers.videoSurveillance.features.1'),
        t('offers.videoSurveillance.features.2'),
        t('offers.videoSurveillance.features.3'),
        t('offers.videoSurveillance.features.4')
      ],
      technologies: [
        t('offers.videoSurveillance.technologies.0'),
        t('offers.videoSurveillance.technologies.1'),
        t('offers.videoSurveillance.technologies.2'),
        t('offers.videoSurveillance.technologies.3')
      ],
      badge: t('offers.videoSurveillance.badge'),
      category: 'surveillance' as const
    },
    {
      icon: <Bell className="w-6 h-6" />,
      title: t('offers.alarmSystems.title'),
      description: t('offers.alarmSystems.description'),
      features: [
        t('offers.alarmSystems.features.0'),
        t('offers.alarmSystems.features.1'),
        t('offers.alarmSystems.features.2'),
        t('offers.alarmSystems.features.3'),
        t('offers.alarmSystems.features.4')
      ],
      technologies: [
        t('offers.alarmSystems.technologies.0'),
        t('offers.alarmSystems.technologies.1'),
        t('offers.alarmSystems.technologies.2'),
        t('offers.alarmSystems.technologies.3')
      ],
      badge: t('offers.alarmSystems.badge'),
      category: 'security' as const
    }
  ];

  const integrationFeatures = [
    {
      icon: <Zap className="w-6 h-6 text-purple-600" />,
      title: t('offers.integration.automation.title'),
      description: t('offers.integration.automation.description')
    },
    {
      icon: <Smartphone className="w-6 h-6 text-blue-600" />,
      title: t('offers.integration.mobile.title'),
      description: t('offers.integration.mobile.description')
    },
    {
      icon: <Activity className="w-6 h-6 text-green-600" />,
      title: t('offers.integration.monitoring.title'),
      description: t('offers.integration.monitoring.description')
    },
    {
      icon: <Shield className="w-6 h-6 text-amber-600" />,
      title: t('offers.integration.security.title'),
      description: t('offers.integration.security.description')
    }
  ];

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-12 sm:mb-16">
          <Badge variant="secondary" className="mb-4 px-3 py-1 text-sm bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800">
            <Settings className="w-4 h-4 mr-2" />
            {t('offers.badge')}
          </Badge>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            {t('offers.title')}
          </h2>
          
          <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed">
            {t('offers.description')}
          </p>
        </div>

        {/* Service Offers Grid */}
        <div className="grid lg:grid-cols-3 gap-6 lg:gap-8 mb-16">
          {serviceOffers.map((offer, index) => (
            <ServiceOffer
              key={index}
              icon={offer.icon}
              title={offer.title}
              description={offer.description}
              features={offer.features}
              technologies={offer.technologies}
              badge={offer.badge}
              category={offer.category}
            />
          ))}
        </div>

        {/* Integration Features */}
        <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 rounded-2xl p-8 sm:p-12 border border-purple-200 dark:border-purple-800">
          <div className="text-center mb-8">
            <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
              {t('offers.integration.title')}
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {t('offers.integration.description')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {integrationFeatures.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-white dark:bg-gray-900 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-sm">
                  {feature.icon}
                </div>
                <h4 className="font-semibold text-foreground mb-2">{feature.title}</h4>
                <p className="text-sm text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Use Cases */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-8">
            {t('offers.useCases.title')}
          </h3>
          
          <div className="grid md:grid-cols-3 gap-6">
            <Card className="p-6 bg-white dark:bg-gray-900 border border-purple-200 dark:border-purple-800">
              <CardContent className="p-0 text-center">
                <Users className="w-8 h-8 text-purple-600 mx-auto mb-4" />
                <h4 className="font-semibold text-foreground mb-2">{t('offers.useCases.nursing.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('offers.useCases.nursing.description')}</p>
              </CardContent>
            </Card>

            <Card className="p-6 bg-white dark:bg-gray-900 border border-blue-200 dark:border-blue-800">
              <CardContent className="p-0 text-center">
                <Activity className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                <h4 className="font-semibold text-foreground mb-2">{t('offers.useCases.hospitals.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('offers.useCases.hospitals.description')}</p>
              </CardContent>
            </Card>

            <Card className="p-6 bg-white dark:bg-gray-900 border border-amber-200 dark:border-amber-800">
              <CardContent className="p-0 text-center">
                <Home className="w-8 h-8 text-amber-600 mx-auto mb-4" />
                <h4 className="font-semibold text-foreground mb-2">{t('offers.useCases.assisted.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('offers.useCases.assisted.description')}</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
