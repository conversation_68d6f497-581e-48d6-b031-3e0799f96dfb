'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Stethoscope, 
  Clock, 
  Shield, 
  Users, 
  TrendingUp, 
  Award,
  CheckCircle,
  Heart,
  Activity,
  FileText,
  Smartphone,
  Zap
} from 'lucide-react';

interface FeatureBenefitProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  benefits: string[];
  metrics?: {
    value: string;
    label: string;
    improvement?: string;
  };
  category: 'clinical' | 'operational' | 'technical' | 'compliance';
}

function FeatureBenefit({ icon, title, description, benefits, metrics, category }: FeatureBenefitProps) {
  const categoryStyles = {
    clinical: 'border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/50',
    operational: 'border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50',
    technical: 'border-purple-200 bg-purple-50/50 dark:border-purple-800 dark:bg-purple-950/50',
    compliance: 'border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50'
  };

  const iconStyles = {
    clinical: 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900',
    operational: 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900',
    technical: 'text-purple-600 bg-purple-100 dark:text-purple-400 dark:bg-purple-900',
    compliance: 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900'
  };

  return (
    <Card className={`h-full transition-all duration-200 hover:shadow-lg ${categoryStyles[category]}`}>
      <CardHeader className="space-y-4">
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${iconStyles[category]}`}>
          {icon}
        </div>
        <CardTitle className="text-xl font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <p className="text-muted-foreground leading-relaxed">{description}</p>
        
        <ul className="space-y-2">
          {benefits.map((benefit, index) => (
            <li key={index} className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-muted-foreground">{benefit}</span>
            </li>
          ))}
        </ul>

        {metrics && (
          <div className="p-4 bg-white/50 dark:bg-gray-900/50 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-foreground">{metrics.value}</div>
                <div className="text-sm text-muted-foreground">{metrics.label}</div>
              </div>
              {metrics.improvement && (
                <div className="flex items-center space-x-1 text-green-600">
                  <TrendingUp className="w-4 h-4" />
                  <span className="text-sm font-medium">{metrics.improvement}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export function TelemonitoringFeaturesSection() {
  const t = useTranslations('telemonitoring');

  const features = [
    {
      icon: <Stethoscope className="w-6 h-6" />,
      title: t('features.clinical.title'),
      description: t('features.clinical.description'),
      benefits: [
        t('features.clinical.benefits.0'),
        t('features.clinical.benefits.1'),
        t('features.clinical.benefits.2'),
        t('features.clinical.benefits.3')
      ],
      metrics: {
        value: t('features.clinical.metrics.value'),
        label: t('features.clinical.metrics.label'),
        improvement: t('features.clinical.metrics.improvement')
      },
      category: 'clinical' as const
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: t('features.efficiency.title'),
      description: t('features.efficiency.description'),
      benefits: [
        t('features.efficiency.benefits.0'),
        t('features.efficiency.benefits.1'),
        t('features.efficiency.benefits.2'),
        t('features.efficiency.benefits.3')
      ],
      metrics: {
        value: t('features.efficiency.metrics.value'),
        label: t('features.efficiency.metrics.label'),
        improvement: t('features.efficiency.metrics.improvement')
      },
      category: 'operational' as const
    },
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: t('features.technology.title'),
      description: t('features.technology.description'),
      benefits: [
        t('features.technology.benefits.0'),
        t('features.technology.benefits.1'),
        t('features.technology.benefits.2'),
        t('features.technology.benefits.3')
      ],
      metrics: {
        value: t('features.technology.metrics.value'),
        label: t('features.technology.metrics.label')
      },
      category: 'technical' as const
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: t('features.compliance.title'),
      description: t('features.compliance.description'),
      benefits: [
        t('features.compliance.benefits.0'),
        t('features.compliance.benefits.1'),
        t('features.compliance.benefits.2'),
        t('features.compliance.benefits.3')
      ],
      metrics: {
        value: t('features.compliance.metrics.value'),
        label: t('features.compliance.metrics.label')
      },
      category: 'compliance' as const
    }
  ];

  const outcomes = [
    {
      icon: <Heart className="w-8 h-8 text-red-600" />,
      title: t('features.outcomes.patient.title'),
      description: t('features.outcomes.patient.description'),
      metric: t('features.outcomes.patient.metric')
    },
    {
      icon: <Users className="w-8 h-8 text-blue-600" />,
      title: t('features.outcomes.provider.title'),
      description: t('features.outcomes.provider.description'),
      metric: t('features.outcomes.provider.metric')
    },
    {
      icon: <TrendingUp className="w-8 h-8 text-green-600" />,
      title: t('features.outcomes.cost.title'),
      description: t('features.outcomes.cost.description'),
      metric: t('features.outcomes.cost.metric')
    },
    {
      icon: <Award className="w-8 h-8 text-purple-600" />,
      title: t('features.outcomes.quality.title'),
      description: t('features.outcomes.quality.description'),
      metric: t('features.outcomes.quality.metric')
    }
  ];

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-12 sm:mb-16">
          <Badge variant="secondary" className="mb-4 px-3 py-1 text-sm bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800">
            <Activity className="w-4 h-4 mr-2" />
            {t('features.badge')}
          </Badge>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            {t('features.title')}
          </h2>
          
          <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed">
            {t('features.description')}
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 gap-6 lg:gap-8 mb-16">
          {features.map((feature, index) => (
            <FeatureBenefit
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              benefits={feature.benefits}
              metrics={feature.metrics}
              category={feature.category}
            />
          ))}
        </div>

        {/* Clinical Outcomes */}
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-2xl p-8 sm:p-12 border border-blue-200 dark:border-blue-800">
          <div className="text-center mb-8">
            <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
              {t('features.outcomes.title')}
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {t('features.outcomes.description')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {outcomes.map((outcome, index) => (
              <div key={index} className="text-center bg-white dark:bg-gray-900 rounded-lg p-6 shadow-sm">
                <div className="flex justify-center mb-4">
                  {outcome.icon}
                </div>
                <h4 className="font-semibold text-foreground mb-2">{outcome.title}</h4>
                <p className="text-sm text-muted-foreground mb-3">{outcome.description}</p>
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{outcome.metric}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Success Stories */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-8">
            {t('features.success.title')}
          </h3>
          
          <div className="grid md:grid-cols-3 gap-6">
            <Card className="p-6 bg-white dark:bg-gray-900 border border-blue-200 dark:border-blue-800">
              <CardContent className="p-0 text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">{t('features.success.case1.metric')}</div>
                <h4 className="font-semibold text-foreground mb-2">{t('features.success.case1.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('features.success.case1.description')}</p>
              </CardContent>
            </Card>

            <Card className="p-6 bg-white dark:bg-gray-900 border border-green-200 dark:border-green-800">
              <CardContent className="p-0 text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">{t('features.success.case2.metric')}</div>
                <h4 className="font-semibold text-foreground mb-2">{t('features.success.case2.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('features.success.case2.description')}</p>
              </CardContent>
            </Card>

            <Card className="p-6 bg-white dark:bg-gray-900 border border-purple-200 dark:border-purple-800">
              <CardContent className="p-0 text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">{t('features.success.case3.metric')}</div>
                <h4 className="font-semibold text-foreground mb-2">{t('features.success.case3.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('features.success.case3.description')}</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
