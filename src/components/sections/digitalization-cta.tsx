'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Smartphone, 
  CheckCircle, 
  ArrowRight, 
  Phone, 
  Mail, 
  Calendar,
  Users,
  Clock,
  TrendingUp,
  Heart
} from 'lucide-react';

interface StatCardProps {
  icon: React.ReactNode;
  value: string;
  label: string;
  description: string;
}

function StatCard({ icon, value, label, description }: StatCardProps) {
  return (
    <Card className="text-center p-6 bg-white/50 dark:bg-gray-900/50 border-green-200 dark:border-green-800">
      <CardContent className="p-0 space-y-3">
        <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto">
          {icon}
        </div>
        <div className="space-y-1">
          <div className="text-2xl font-bold text-foreground">{value}</div>
          <div className="text-sm font-medium text-green-600 dark:text-green-400">{label}</div>
          <div className="text-xs text-muted-foreground">{description}</div>
        </div>
      </CardContent>
    </Card>
  );
}

export function DigitalizationCTASection() {
  const t = useTranslations('digitalization');
  const tCta = useTranslations('cta');
  const locale = useLocale();

  const stats = [
    {
      icon: <Users className="w-6 h-6 text-green-600 dark:text-green-400" />,
      value: t('cta.stats.facilities'),
      label: t('cta.stats.facilitiesLabel'),
      description: t('cta.stats.facilitiesDescription')
    },
    {
      icon: <Clock className="w-6 h-6 text-green-600 dark:text-green-400" />,
      value: t('cta.stats.efficiency'),
      label: t('cta.stats.efficiencyLabel'),
      description: t('cta.stats.efficiencyDescription')
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-green-600 dark:text-green-400" />,
      value: t('cta.stats.satisfaction'),
      label: t('cta.stats.satisfactionLabel'),
      description: t('cta.stats.satisfactionDescription')
    },
    {
      icon: <Heart className="w-6 h-6 text-green-600 dark:text-green-400" />,
      value: t('cta.stats.quality'),
      label: t('cta.stats.qualityLabel'),
      description: t('cta.stats.qualityDescription')
    }
  ];

  const benefits = [
    t('cta.benefits.0'),
    t('cta.benefits.1'),
    t('cta.benefits.2'),
    t('cta.benefits.3'),
    t('cta.benefits.4'),
    t('cta.benefits.5')
  ];

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-green-50 via-white to-green-50 dark:from-green-950 dark:via-background dark:to-green-950">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Stats Section */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-16">
          {stats.map((stat, index) => (
            <StatCard
              key={index}
              icon={stat.icon}
              value={stat.value}
              label={stat.label}
              description={stat.description}
            />
          ))}
        </div>

        {/* Main CTA */}
        <div className="max-w-4xl mx-auto">
          <Card className="overflow-hidden bg-gradient-to-r from-green-600 to-green-700 border-0 text-white">
            <CardContent className="p-8 sm:p-12 lg:p-16">
              <div className="text-center space-y-8">
                {/* Header */}
                <div className="space-y-4">
                  <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto">
                    <Smartphone className="w-8 h-8 text-white" />
                  </div>
                  
                  <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white">
                    {t('cta.title')}
                  </h2>
                  
                  <p className="text-xl text-green-100 max-w-2xl mx-auto leading-relaxed">
                    {t('cta.description')}
                  </p>
                </div>

                {/* Benefits Grid */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4 text-left">
                  {benefits.map((benefit, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-300 mt-0.5 flex-shrink-0" />
                      <span className="text-green-100 text-sm">{benefit}</span>
                    </div>
                  ))}
                </div>

                {/* CTA Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
                  <Button 
                    size="lg" 
                    className="text-base px-8 py-6 h-auto min-h-[48px] font-medium bg-white text-green-600 hover:bg-green-50 border-0"
                    asChild
                  >
                    <Link href={`/${locale}/schedule-consultation`}>
                      <Calendar className="w-5 h-5 mr-2" />
                      {tCta('scheduleConsultation')}
                    </Link>
                  </Button>

                  <Button 
                    variant="outline" 
                    size="lg" 
                    className="text-base px-8 py-6 h-auto min-h-[48px] font-medium border-white/30 text-white hover:bg-white/10"
                    asChild
                  >
                    <Link href={`/${locale}/services`}>
                      {t('cta.viewAllServices')}
                      <ArrowRight className="w-5 h-5 ml-2" />
                    </Link>
                  </Button>
                </div>

                {/* Contact Options */}
                <div className="pt-8 border-t border-white/20">
                  <p className="text-green-100 mb-4">{t('cta.contactText')}</p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <div className="flex items-center space-x-2 text-green-100">
                      <Phone className="w-4 h-4" />
                      <span className="text-sm">{t('cta.phone')}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-green-100">
                      <Mail className="w-4 h-4" />
                      <span className="text-sm">{t('cta.email')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Information */}
        <div className="text-center mt-12 max-w-2xl mx-auto">
          <p className="text-muted-foreground text-sm leading-relaxed">
            {t('cta.additionalInfo')}
          </p>
        </div>

        {/* Implementation Timeline */}
        <div className="mt-16 bg-white dark:bg-gray-900 rounded-2xl p-8 border border-green-200 dark:border-green-800">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-foreground mb-4">
              {t('cta.timeline.title')}
            </h3>
            <p className="text-muted-foreground">
              {t('cta.timeline.description')}
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-green-600 dark:text-green-400 font-bold">1</span>
              </div>
              <h4 className="font-semibold text-foreground mb-2">{t('cta.timeline.phase1.title')}</h4>
              <p className="text-sm text-muted-foreground">{t('cta.timeline.phase1.description')}</p>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">{t('cta.timeline.phase1.duration')}</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-green-600 dark:text-green-400 font-bold">2</span>
              </div>
              <h4 className="font-semibold text-foreground mb-2">{t('cta.timeline.phase2.title')}</h4>
              <p className="text-sm text-muted-foreground">{t('cta.timeline.phase2.description')}</p>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">{t('cta.timeline.phase2.duration')}</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-green-600 dark:text-green-400 font-bold">3</span>
              </div>
              <h4 className="font-semibold text-foreground mb-2">{t('cta.timeline.phase3.title')}</h4>
              <p className="text-sm text-muted-foreground">{t('cta.timeline.phase3.description')}</p>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">{t('cta.timeline.phase3.duration')}</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-green-600 dark:text-green-400 font-bold">4</span>
              </div>
              <h4 className="font-semibold text-foreground mb-2">{t('cta.timeline.phase4.title')}</h4>
              <p className="text-sm text-muted-foreground">{t('cta.timeline.phase4.description')}</p>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">{t('cta.timeline.phase4.duration')}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
