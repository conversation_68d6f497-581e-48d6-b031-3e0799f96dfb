'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Mail,
  Phone,
  MapPin,
  Globe,
  Linkedin,
  Twitter,
  Youtube,
  ExternalLink
} from 'lucide-react';
import { LanguageSwitcher } from '../language-switcher';
import { useTranslations, useLocale } from 'next-intl';

interface FooterLinkProps {
  href: string;
  children: React.ReactNode;
  external?: boolean;
}

function FooterLink({ href, children, external }: FooterLinkProps) {
  return (
    <Link
      href={href}
      className="text-muted-foreground hover:text-primary transition-colors text-sm flex items-center"
      {...(external && { target: "_blank", rel: "noopener noreferrer" })}
    >
      {children}
      {external && <ExternalLink className="w-3 h-3 ml-1" />}
    </Link>
  );
}

export function Footer() {
  const currentYear = new Date().getFullYear();
  const t = useTranslations('footer');
  const locale = useLocale();

  return (
    <footer className="bg-muted/30 border-t border-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-12 sm:py-16">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-10 lg:gap-8">
            {/* Company Info */}
            <div className="space-y-4 sm:col-span-2 lg:col-span-1">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-primary-foreground font-bold text-sm">A</span>
                </div>
                <div className="min-w-0">
                  <h3 className="text-lg font-bold text-foreground">AITELMED</h3>
                  <Badge variant="secondary" className="text-xs px-2 py-0.5">
                    GBA-konform | Klasse IIa
                  </Badge>
                </div>
              </div>
              <p className="text-muted-foreground text-sm leading-relaxed">
                {t('description')}
              </p>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <MapPin className="w-4 h-4 flex-shrink-0" />
                  <span>{t('contact.address')}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Phone className="w-4 h-4 flex-shrink-0" />
                  <a
                    href={`tel:${t('contact.phone')}`}
                    className="hover:text-primary transition-colors"
                    aria-label={`Call ${t('contact.phone')}`}
                  >
                    {t('contact.phone')}
                  </a>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Mail className="w-4 h-4 flex-shrink-0" />
                  <a
                    href={`mailto:${t('contact.email')}`}
                    className="hover:text-primary transition-colors"
                    aria-label={`Email ${t('contact.email')}`}
                  >
                    {t('contact.email')}
                  </a>
                </div>
              </div>
            </div>

            {/* Services */}
            <div className="space-y-3 sm:space-y-4">
              <h4 className="font-semibold text-foreground text-sm sm:text-base">{t('services')}</h4>
              <div className="space-y-1.5 sm:space-y-2">
                <FooterLink href="#medpower">{t('links.services.medpower')}</FooterLink>
                <FooterLink href={`/${locale}/services/telemonitoring`}>{t('links.services.telemonitoring')}</FooterLink>
                <FooterLink href="#patient-surveys">{t('links.services.patientSurveys')}</FooterLink>
                <FooterLink href={`/${locale}/configurator`}>{t('links.services.testPlatform')}</FooterLink>
                <FooterLink href="#integration">{t('links.services.integration')}</FooterLink>
              </div>
            </div>

            {/* Support & Resources */}
            <div className="space-y-3 sm:space-y-4">
              <h4 className="font-semibold text-foreground text-sm sm:text-base">{t('support')}</h4>
              <div className="space-y-1.5 sm:space-y-2">
                <FooterLink href={`/${locale}/request-demo`}>{t('links.support.requestDemo')}</FooterLink>
                <FooterLink href={`/${locale}/schedule-consultation`}>{t('links.support.scheduleConsultation')}</FooterLink>
                <FooterLink href="#documentation" external>{t('links.support.documentation')}</FooterLink>
                <FooterLink href="#support" external>{t('links.support.technicalSupport')}</FooterLink>
                <FooterLink href="#training">{t('links.support.trainingResources')}</FooterLink>
                <FooterLink href="#partner-portal" external>{t('links.support.partnerPortal')}</FooterLink>
              </div>
            </div>

            {/* Company & Legal */}
            <div className="space-y-3 sm:space-y-4">
              <h4 className="font-semibold text-foreground text-sm sm:text-base">{t('company')}</h4>
              <div className="space-y-1.5 sm:space-y-2">
                <FooterLink href="#about">{t('links.company.about')}</FooterLink>
                <FooterLink href="#careers">{t('links.company.careers')}</FooterLink>
                <FooterLink href="#news">{t('links.company.news')}</FooterLink>
                <FooterLink href="#contact">{t('links.company.contact')}</FooterLink>
                <FooterLink href="#privacy">{t('links.company.privacy')}</FooterLink>
                <FooterLink href="#terms">{t('links.company.terms')}</FooterLink>
                <FooterLink href="#compliance">{t('links.company.compliance')}</FooterLink>
                <FooterLink href={`/${locale}/impressum`}>{t('links.company.impressum')}</FooterLink>
              </div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Bottom Footer */}
        <div className="py-6 sm:py-8">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0 gap-4">
            {/* Copyright */}
            <div className="text-xs sm:text-sm text-muted-foreground text-center lg:text-left order-3 lg:order-1">
              © {currentYear} AITELMED. {t('copyright')}
            </div>

            {/* Social Links */}
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 order-1 lg:order-2">
              <span className="text-xs sm:text-sm text-muted-foreground">{t('social.followUs')}</span>
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" className="w-8 h-8 p-0 min-h-[44px] min-w-[44px]" asChild>
                  <Link href="#linkedin" target="_blank" rel="noopener noreferrer">
                    <Linkedin className="w-4 h-4" />
                    <span className="sr-only">{t('social.linkedin')}</span>
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" className="w-8 h-8 p-0 min-h-[44px] min-w-[44px]" asChild>
                  <Link href="#twitter" target="_blank" rel="noopener noreferrer">
                    <Twitter className="w-4 h-4" />
                    <span className="sr-only">{t('social.twitter')}</span>
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" className="w-8 h-8 p-0 min-h-[44px] min-w-[44px]" asChild>
                  <Link href="#youtube" target="_blank" rel="noopener noreferrer">
                    <Youtube className="w-4 h-4" />
                    <span className="sr-only">{t('social.youtube')}</span>
                  </Link>
                </Button>
              </div>
            </div>

            {/* Language Selector */}
            <div className="order-2 lg:order-3">
              <LanguageSwitcher variant="select" />
            </div>
          </div>
        </div>

        {/* Final CTA Strip */}
        <div className="py-6 sm:py-8 border-t border-border">
          <div className="text-center space-y-4 sm:space-y-6">
            <h3 className="text-lg sm:text-xl font-semibold text-foreground leading-tight">
              {t('ctaTitle')}
            </h3>
            <p className="text-sm sm:text-base text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              {t('ctaDescription')}
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md sm:max-w-none mx-auto">
              <Button size="lg" className="min-h-[48px]" asChild>
                <Link href={`/${locale}/request-demo`}>
                  {t('cta.requestDemo')}
                </Link>
              </Button>
              <Button variant="outline" size="lg" className="min-h-[48px]" asChild>
                <Link href={`/${locale}/schedule-consultation`}>
                  {t('cta.scheduleConsultation')}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
