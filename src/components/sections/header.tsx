'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetTrigger, SheetTitle } from '@/components/ui/sheet';
import { VisuallyHidden } from '@/components/ui/visually-hidden';
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger } from '@/components/ui/navigation-menu';

import { LanguageSwitcher } from '@/components/language-switcher';
import { ThemeToggle, ThemeToggleCompact } from '@/components/theme-toggle';
import { Menu } from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavigationItem {
  label: string;
  href: string;
  external?: boolean;
  children?: NavigationItem[];
}

function useNavigationItems(): NavigationItem[] {
  const t = useTranslations('navigation');
  const locale = useLocale();

  return [
    {
      label: t('services'),
      href: `/${locale}/services`,
      children: [
        { label: t('iomt'), href: `/${locale}/services/iomt` },
        { label: t('digitalization'), href: `/${locale}/services/digitalisierung` },
        { label: t('telemonitoring'), href: `/${locale}/services/telemonitoring` },
        { label: t('smartBuildings'), href: `/${locale}/services/weitere-angebote` },
      ],
    },
    {
      label: t('features'),
      href: '#features',
      children: [
        { label: t('vitalData'), href: '#vital-data' },
        { label: t('alarmSystem'), href: '#alarm-system' },
        { label: t('patientSurveys'), href: '#patient-surveys' },
        { label: t('integration'), href: '#integration' },
      ],
    },
    { label: t('company'), href: '#company' },
    { label: t('contact'), href: '#contact' },
  ];
}

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  // Theme hook no longer needed for mounted state
  const t = useTranslations('common');
  const tCta = useTranslations('cta');
  const locale = useLocale();
  const navigationItems = useNavigationItems();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleMobileMenuClose = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <header
      className={cn(
        'sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 transition-all duration-200',
        isScrolled && 'shadow-sm'
      )}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-14 sm:h-16 items-center justify-between">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1 sm:flex-initial">
            <Link href={`/${locale}`} className="flex items-center space-x-2 sm:space-x-3 hover:opacity-80 transition-opacity min-w-0">
              <div className="flex items-center space-x-1.5 sm:space-x-2 min-w-0">
                <div className="w-7 h-7 sm:w-8 sm:h-8 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-primary-foreground font-bold text-xs sm:text-sm">A</span>
                </div>
                <div className="flex flex-col min-w-0">
                  <span className="text-lg sm:text-xl font-bold text-foreground truncate">{t('company')}</span>
                  <Badge variant="secondary" className="text-xs px-1.5 sm:px-2 py-0 h-3.5 sm:h-4 hidden xs:inline-flex">
                    {t('certification')}
                  </Badge>
                </div>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-6 xl:space-x-8">
            <NavigationMenu>
              <NavigationMenuList>
                {navigationItems.map((item) => (
                  <NavigationMenuItem key={item.label}>
                    {item.children ? (
                      <>
                        <NavigationMenuTrigger className="text-foreground hover:text-primary transition-colors text-sm font-medium min-h-[44px] h-auto px-3 py-2 flex items-center justify-center">
                          {item.label}
                        </NavigationMenuTrigger>
                        <NavigationMenuContent>
                          <ul className="grid w-[300px] gap-1 p-2 lg:w-[400px] lg:grid-cols-2 xl:w-[500px]">
                            {item.children.map((child) => (
                              <li key={child.label}>
                                <NavigationMenuLink asChild>
                                  <Link
                                    href={child.href}
                                    className="block select-none rounded-md p-2 leading-tight no-underline outline-none transition-colors hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary"
                                  >
                                    <div className="text-sm font-medium">{child.label}</div>
                                  </Link>
                                </NavigationMenuLink>
                              </li>
                            ))}
                          </ul>
                        </NavigationMenuContent>
                      </>
                    ) : (
                      <NavigationMenuLink asChild>
                        <Link
                          href={item.href}
                          className="text-foreground hover:text-primary transition-colors px-3 py-2 text-sm font-medium min-h-[44px] flex items-center justify-center"
                        >
                          {item.label}
                        </Link>
                      </NavigationMenuLink>
                    )}
                  </NavigationMenuItem>
                ))}
              </NavigationMenuList>
            </NavigationMenu>
          </nav>

          {/* Desktop CTAs, Language Switcher and Theme Toggle */}
          <div className="hidden lg:flex items-center space-x-3 xl:space-x-4">
            {/* Language Switcher */}
            <LanguageSwitcher variant="select" />

            {/* Theme Toggle */}
            <ThemeToggle />

            <Button variant="outline" size="sm" className="min-h-[44px] px-4" asChild>
              <Link href={`/${locale}/schedule-consultation`}>{tCta('scheduleConsultation')}</Link>
            </Button>
            {/* <Button size="sm" className="min-h-[44px]" asChild>
              <Link href={`/${locale}/request-demo`}>{tCta('requestDemo')}</Link>
            </Button> */}
          </div>

          {/* Mobile/Tablet Menu Button */}
          <div className="lg:hidden flex items-center justify-center space-x-2">
            {/* Mobile Theme Toggle */}
            <div className="hidden sm:flex sm:items-center sm:justify-center">
              <ThemeToggleCompact className="min-h-[44px] min-w-[44px] flex items-center justify-center" />
            </div>

            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="min-h-[44px] min-w-[44px] flex items-center justify-center">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[300px] sm:w-[380px] overflow-y-auto">
                <VisuallyHidden>
                  <SheetTitle>Navigation Menu</SheetTitle>
                </VisuallyHidden>
                <div className="flex flex-col space-y-8 mt-8 px-2">
                  {/* Mobile Navigation */}
                  <nav className="flex flex-col space-y-4">
                    {navigationItems.map((item) => (
                      <div key={item.label}>
                        {item.children ? (
                          <div className="space-y-3">
                            <div className="font-bold text-foreground text-lg py-3 px-3 border-b-2 border-primary/20 bg-primary/5 rounded-lg">{item.label}</div>
                            <div className="pl-4 space-y-2">
                              {item.children.map((child) => (
                                <Link
                                  key={child.label}
                                  href={child.href}
                                  className="block text-base text-muted-foreground hover:text-primary transition-all duration-200 py-3 px-4 rounded-lg hover:bg-primary/10 hover:shadow-sm min-h-[48px] flex items-center border border-transparent hover:border-primary/20"
                                  onClick={handleMobileMenuClose}
                                >
                                  {child.label}
                                </Link>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <Link
                            href={item.href}
                            className="block font-bold text-foreground hover:text-primary transition-all duration-200 py-4 px-4 rounded-lg hover:bg-primary/10 hover:shadow-sm min-h-[52px] flex items-center border-b-2 border-primary/20 bg-primary/5 text-lg"
                            onClick={handleMobileMenuClose}
                          >
                            {item.label}
                          </Link>
                        )}
                      </div>
                    ))}
                  </nav>

                  {/* Mobile CTAs */}
                  <div className="flex flex-col space-y-4 pt-8 border-t-2 border-primary/20">
                    <Button variant="outline" className="min-h-[52px] text-lg font-semibold rounded-lg border-2 hover:border-primary hover:bg-primary/10" asChild>
                      <Link href={`/${locale}/schedule-consultation`} onClick={handleMobileMenuClose}>
                        {tCta('scheduleConsultation')}
                      </Link>
                    </Button>
                    {/* <Button className="min-h-[52px] text-lg font-semibold rounded-lg" asChild>
                      <Link href={`/${locale}/request-demo`} onClick={handleMobileMenuClose}>
                        {tCta('requestDemo')}
                      </Link>
                    </Button> */}
                  </div>

                  {/* Mobile Language Switcher */}
                  <div className="pt-8 border-t-2 border-primary/20">
                    <label className="text-base font-bold text-foreground mb-4 block">
                      Language / Sprache / Język
                    </label>
                    <LanguageSwitcher variant="buttons" className="justify-start gap-3" />
                  </div>

                  {/* Mobile Theme Toggle */}
                  <div className="pt-8 border-t-2 border-primary/20 sm:hidden">
                    <label className="text-base font-bold text-foreground mb-4 block">
                      Theme / Design
                    </label>
                    <ThemeToggleCompact className="min-h-[48px] min-w-[48px]" />
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
}
