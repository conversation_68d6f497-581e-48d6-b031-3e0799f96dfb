'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  Clock, 
  DollarSign, 
  Users, 
  Shield, 
  Award,
  CheckCircle,
  BarChart3,
  Heart,
  Zap
} from 'lucide-react';

interface BenefitCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  metrics: {
    value: string;
    label: string;
    improvement?: string;
  }[];
  category: 'efficiency' | 'quality' | 'cost' | 'compliance';
}

function BenefitCard({ icon, title, description, metrics, category }: BenefitCardProps) {
  const categoryStyles = {
    efficiency: 'border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50',
    quality: 'border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50',
    cost: 'border-purple-200 bg-purple-50/50 dark:border-purple-800 dark:bg-purple-950/50',
    compliance: 'border-amber-200 bg-amber-50/50 dark:border-amber-800 dark:bg-amber-950/50'
  };

  const iconStyles = {
    efficiency: 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900',
    quality: 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900',
    cost: 'text-purple-600 bg-purple-100 dark:text-purple-400 dark:bg-purple-900',
    compliance: 'text-amber-600 bg-amber-100 dark:text-amber-400 dark:bg-amber-900'
  };

  return (
    <Card className={`h-full transition-all duration-200 hover:shadow-lg ${categoryStyles[category]}`}>
      <CardHeader className="space-y-4">
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${iconStyles[category]}`}>
          {icon}
        </div>
        <CardTitle className="text-xl font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <p className="text-muted-foreground leading-relaxed">{description}</p>
        
        <div className="space-y-4">
          {metrics.map((metric, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-white/50 dark:bg-gray-900/50 rounded-lg border">
              <div className="flex-1">
                <div className="text-2xl font-bold text-foreground">{metric.value}</div>
                <div className="text-sm text-muted-foreground">{metric.label}</div>
              </div>
              {metric.improvement && (
                <div className="flex items-center space-x-1 text-green-600">
                  <TrendingUp className="w-4 h-4" />
                  <span className="text-sm font-medium">{metric.improvement}</span>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export function DigitalizationBenefitsSection() {
  const t = useTranslations('digitalization');

  const benefits = [
    {
      icon: <Clock className="w-6 h-6" />,
      title: t('benefits.efficiency.title'),
      description: t('benefits.efficiency.description'),
      metrics: [
        {
          value: t('benefits.efficiency.metrics.time.value'),
          label: t('benefits.efficiency.metrics.time.label'),
          improvement: t('benefits.efficiency.metrics.time.improvement')
        },
        {
          value: t('benefits.efficiency.metrics.response.value'),
          label: t('benefits.efficiency.metrics.response.label'),
          improvement: t('benefits.efficiency.metrics.response.improvement')
        }
      ],
      category: 'efficiency' as const
    },
    {
      icon: <Heart className="w-6 h-6" />,
      title: t('benefits.quality.title'),
      description: t('benefits.quality.description'),
      metrics: [
        {
          value: t('benefits.quality.metrics.satisfaction.value'),
          label: t('benefits.quality.metrics.satisfaction.label'),
          improvement: t('benefits.quality.metrics.satisfaction.improvement')
        },
        {
          value: t('benefits.quality.metrics.errors.value'),
          label: t('benefits.quality.metrics.errors.label'),
          improvement: t('benefits.quality.metrics.errors.improvement')
        }
      ],
      category: 'quality' as const
    },
    {
      icon: <DollarSign className="w-6 h-6" />,
      title: t('benefits.cost.title'),
      description: t('benefits.cost.description'),
      metrics: [
        {
          value: t('benefits.cost.metrics.reduction.value'),
          label: t('benefits.cost.metrics.reduction.label'),
          improvement: t('benefits.cost.metrics.reduction.improvement')
        },
        {
          value: t('benefits.cost.metrics.roi.value'),
          label: t('benefits.cost.metrics.roi.label')
        }
      ],
      category: 'cost' as const
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: t('benefits.compliance.title'),
      description: t('benefits.compliance.description'),
      metrics: [
        {
          value: t('benefits.compliance.metrics.gba.value'),
          label: t('benefits.compliance.metrics.gba.label')
        },
        {
          value: t('benefits.compliance.metrics.documentation.value'),
          label: t('benefits.compliance.metrics.documentation.label'),
          improvement: t('benefits.compliance.metrics.documentation.improvement')
        }
      ],
      category: 'compliance' as const
    }
  ];

  const processImprovements = [
    {
      icon: <Users className="w-6 h-6 text-blue-600" />,
      title: t('benefits.processes.staff.title'),
      description: t('benefits.processes.staff.description'),
      improvement: t('benefits.processes.staff.improvement')
    },
    {
      icon: <BarChart3 className="w-6 h-6 text-green-600" />,
      title: t('benefits.processes.workflow.title'),
      description: t('benefits.processes.workflow.description'),
      improvement: t('benefits.processes.workflow.improvement')
    },
    {
      icon: <Zap className="w-6 h-6 text-purple-600" />,
      title: t('benefits.processes.automation.title'),
      description: t('benefits.processes.automation.description'),
      improvement: t('benefits.processes.automation.improvement')
    },
    {
      icon: <Award className="w-6 h-6 text-amber-600" />,
      title: t('benefits.processes.quality.title'),
      description: t('benefits.processes.quality.description'),
      improvement: t('benefits.processes.quality.improvement')
    }
  ];

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-12 sm:mb-16">
          <Badge variant="secondary" className="mb-4 px-3 py-1 text-sm bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800">
            <TrendingUp className="w-4 h-4 mr-2" />
            {t('benefits.badge')}
          </Badge>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            {t('benefits.title')}
          </h2>
          
          <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed">
            {t('benefits.description')}
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 gap-6 lg:gap-8 mb-16">
          {benefits.map((benefit, index) => (
            <BenefitCard
              key={index}
              icon={benefit.icon}
              title={benefit.title}
              description={benefit.description}
              metrics={benefit.metrics}
              category={benefit.category}
            />
          ))}
        </div>

        {/* Process Improvements */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950 dark:to-blue-950 rounded-2xl p-8 sm:p-12 border border-green-200 dark:border-green-800">
          <div className="text-center mb-8">
            <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
              {t('benefits.processes.title')}
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {t('benefits.processes.description')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {processImprovements.map((process, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-white dark:bg-gray-900 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-sm">
                  {process.icon}
                </div>
                <h4 className="font-semibold text-foreground mb-2">{process.title}</h4>
                <p className="text-sm text-muted-foreground mb-3">{process.description}</p>
                <div className="flex items-center justify-center space-x-1 text-green-600">
                  <TrendingUp className="w-4 h-4" />
                  <span className="text-sm font-medium">{process.improvement}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
