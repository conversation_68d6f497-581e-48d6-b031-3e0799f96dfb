'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Monitor, 
  Heart, 
  Activity, 
  CheckCircle, 
  Award, 
  Clock,
  Users,
  Stethoscope,
  Wifi
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface MonitoringIndicatorProps {
  icon: React.ReactNode;
  value: string;
  label: string;
  status?: 'active' | 'certified' | 'monitoring';
}

function MonitoringIndicator({ icon, value, label, status = 'active' }: MonitoringIndicatorProps) {
  const statusColors = {
    active: 'text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-950 dark:border-blue-800',
    certified: 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-950 dark:border-green-800',
    monitoring: 'text-purple-600 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-950 dark:border-purple-800'
  };

  return (
    <div className="flex items-center space-x-3 text-center sm:text-left">
      <div className={cn(
        "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center border",
        statusColors[status]
      )}>
        {icon}
      </div>
      <div className="min-w-0 flex-1">
        <div className="text-xl font-bold text-foreground">{value}</div>
        <div className="text-sm text-muted-foreground leading-tight">{label}</div>
      </div>
    </div>
  );
}

export function TelemonitoringHeroSection() {
  const t = useTranslations('telemonitoring');
  const tCta = useTranslations('cta');
  const locale = useLocale();

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-background via-background to-blue-50/30 dark:to-blue-950/20">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-24">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Content Column */}
          <div className="space-y-8 order-2 lg:order-1">
            {/* Service Badge */}
            <div className="flex justify-center lg:justify-start">
              <Badge variant="secondary" className="px-4 py-2 text-sm font-medium bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800">
                <Monitor className="w-4 h-4 mr-2" />
                {t('badge')}
              </Badge>
            </div>

            {/* Main Headline */}
            <div className="space-y-4 text-center lg:text-left">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-foreground leading-tight">
                <span className="text-blue-600">{t('hero.title.highlight')}</span>
                <br />
                {t('hero.title.main')}
              </h1>

              <p className="text-xl text-muted-foreground font-medium leading-relaxed">
                {t('hero.subtitle')}
              </p>

              <p className="text-lg text-muted-foreground max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                {t('hero.description')}
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button size="lg" className="text-base px-8 py-6 h-auto min-h-[48px] font-medium bg-blue-600 hover:bg-blue-700" asChild>
                <Link href={`/${locale}/schedule-consultation`}>
                  <Stethoscope className="w-5 h-5 mr-2" />
                  {tCta('scheduleConsultation')}
                </Link>
              </Button>

              <Button variant="outline" size="lg" className="text-base px-8 py-6 h-auto min-h-[48px] font-medium border-blue-200 text-blue-700 hover:bg-blue-50" asChild>
                <Link href={`/${locale}/services`}>
                  {tCta('learnMore')}
                </Link>
              </Button>
            </div>

            {/* Monitoring Indicators */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8 border-t border-border">
              <MonitoringIndicator
                icon={<Users className="w-5 h-5" />}
                value={t('indicators.patients')}
                label={t('indicators.patientsLabel')}
                status="monitoring"
              />
              <MonitoringIndicator
                icon={<Award className="w-5 h-5" />}
                value={t('indicators.certification')}
                label={t('indicators.certificationLabel')}
                status="certified"
              />
              <MonitoringIndicator
                icon={<Clock className="w-5 h-5" />}
                value={t('indicators.availability')}
                label={t('indicators.availabilityLabel')}
                status="active"
              />
            </div>
          </div>

          {/* Visual Column */}
          <div className="relative order-1 lg:order-2">
            {/* medPower® Platform Mockup */}
            <div className="relative aspect-[4/3] rounded-2xl overflow-hidden bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 border border-blue-200 dark:border-blue-800">
              <div className="absolute inset-0 flex items-center justify-center p-6">
                <div className="text-center space-y-4 w-full">
                  <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto">
                    <Monitor className="w-10 h-10 text-white" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold text-foreground">{t('visual.title')}</h3>
                    <p className="text-base text-muted-foreground">{t('visual.subtitle')}</p>
                  </div>
                  
                  {/* Mock Monitoring Cards */}
                  <div className="grid grid-cols-2 gap-3 mt-6">
                    <Card className="p-3 bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800">
                      <CardContent className="p-0 text-center">
                        <Heart className="w-6 h-6 text-green-600 mx-auto mb-1" />
                        <div className="text-xs font-medium text-green-700 dark:text-green-300">Vitaldaten</div>
                      </CardContent>
                    </Card>
                    <Card className="p-3 bg-purple-50 border-purple-200 dark:bg-purple-950 dark:border-purple-800">
                      <CardContent className="p-0 text-center">
                        <Activity className="w-6 h-6 text-purple-600 mx-auto mb-1" />
                        <div className="text-xs font-medium text-purple-700 dark:text-purple-300">Monitoring</div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>

              {/* Floating Status Elements */}
              <div className="absolute top-4 right-4">
                <Card className="p-2 shadow-lg bg-white/90 dark:bg-gray-900/90">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-xs font-medium">{t('visual.statusActive')}</span>
                  </CardContent>
                </Card>
              </div>

              <div className="absolute bottom-4 left-4">
                <Card className="p-2 shadow-lg bg-white/90 dark:bg-gray-900/90">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <Award className="w-3 h-3 text-blue-600" />
                    <span className="text-xs font-medium">{t('visual.statusCertified')}</span>
                  </CardContent>
                </Card>
              </div>

              <div className="absolute top-4 left-4">
                <Card className="p-2 shadow-lg bg-white/90 dark:bg-gray-900/90">
                  <CardContent className="p-0 flex items-center space-x-2">
                    <Wifi className="w-3 h-3 text-blue-600" />
                    <span className="text-xs font-medium">{t('visual.statusConnected')}</span>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Background Decorations */}
            <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-500/10 rounded-full blur-xl" />
            <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-blue-600/10 rounded-full blur-xl" />
          </div>
        </div>

        {/* medPower® Brand Highlight */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-2xl p-8 border border-blue-200 dark:border-blue-800">
            <div className="max-w-3xl mx-auto">
              <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
                {t('brand.title')}
              </h3>
              <p className="text-lg text-muted-foreground mb-6">
                {t('brand.description')}
              </p>
              <div className="flex flex-wrap justify-center gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>{t('brand.feature1')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>{t('brand.feature2')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span>{t('brand.feature3')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
