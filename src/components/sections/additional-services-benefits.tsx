'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  Shield, 
  Clock, 
  Users, 
  DollarSign, 
  Award,
  CheckCircle,
  Heart,
  Zap,
  Lock,
  Activity,
  AlertTriangle
} from 'lucide-react';

interface BenefitMetricProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  metrics: {
    primary: {
      value: string;
      label: string;
      improvement?: string;
    };
    secondary?: {
      value: string;
      label: string;
    };
  };
  category: 'safety' | 'efficiency' | 'cost' | 'quality';
}

function BenefitMetric({ icon, title, description, metrics, category }: BenefitMetricProps) {
  const categoryStyles = {
    safety: 'border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/50',
    efficiency: 'border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50',
    cost: 'border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50',
    quality: 'border-purple-200 bg-purple-50/50 dark:border-purple-800 dark:bg-purple-950/50'
  };

  const iconStyles = {
    safety: 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900',
    efficiency: 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900',
    cost: 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900',
    quality: 'text-purple-600 bg-purple-100 dark:text-purple-400 dark:bg-purple-900'
  };

  return (
    <Card className={`h-full transition-all duration-200 hover:shadow-lg ${categoryStyles[category]}`}>
      <CardHeader className="space-y-4">
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${iconStyles[category]}`}>
          {icon}
        </div>
        <CardTitle className="text-xl font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <p className="text-muted-foreground leading-relaxed">{description}</p>
        
        <div className="space-y-4">
          <div className="p-4 bg-white/50 dark:bg-gray-900/50 rounded-lg border">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-foreground">{metrics.primary.value}</div>
                <div className="text-sm text-muted-foreground">{metrics.primary.label}</div>
              </div>
              {metrics.primary.improvement && (
                <div className="flex items-center space-x-1 text-green-600">
                  <TrendingUp className="w-4 h-4" />
                  <span className="text-sm font-medium">{metrics.primary.improvement}</span>
                </div>
              )}
            </div>
          </div>

          {metrics.secondary && (
            <div className="p-3 bg-white/30 dark:bg-gray-900/30 rounded-lg border">
              <div className="text-lg font-semibold text-foreground">{metrics.secondary.value}</div>
              <div className="text-xs text-muted-foreground">{metrics.secondary.label}</div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function AdditionalServicesBenefitsSection() {
  const t = useTranslations('additionalServices');

  const benefits = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: t('benefits.safety.title'),
      description: t('benefits.safety.description'),
      metrics: {
        primary: {
          value: t('benefits.safety.metrics.incidents.value'),
          label: t('benefits.safety.metrics.incidents.label'),
          improvement: t('benefits.safety.metrics.incidents.improvement')
        },
        secondary: {
          value: t('benefits.safety.metrics.response.value'),
          label: t('benefits.safety.metrics.response.label')
        }
      },
      category: 'safety' as const
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: t('benefits.efficiency.title'),
      description: t('benefits.efficiency.description'),
      metrics: {
        primary: {
          value: t('benefits.efficiency.metrics.automation.value'),
          label: t('benefits.efficiency.metrics.automation.label'),
          improvement: t('benefits.efficiency.metrics.automation.improvement')
        },
        secondary: {
          value: t('benefits.efficiency.metrics.staff.value'),
          label: t('benefits.efficiency.metrics.staff.label')
        }
      },
      category: 'efficiency' as const
    },
    {
      icon: <DollarSign className="w-6 h-6" />,
      title: t('benefits.cost.title'),
      description: t('benefits.cost.description'),
      metrics: {
        primary: {
          value: t('benefits.cost.metrics.savings.value'),
          label: t('benefits.cost.metrics.savings.label'),
          improvement: t('benefits.cost.metrics.savings.improvement')
        },
        secondary: {
          value: t('benefits.cost.metrics.roi.value'),
          label: t('benefits.cost.metrics.roi.label')
        }
      },
      category: 'cost' as const
    },
    {
      icon: <Heart className="w-6 h-6" />,
      title: t('benefits.quality.title'),
      description: t('benefits.quality.description'),
      metrics: {
        primary: {
          value: t('benefits.quality.metrics.satisfaction.value'),
          label: t('benefits.quality.metrics.satisfaction.label'),
          improvement: t('benefits.quality.metrics.satisfaction.improvement')
        },
        secondary: {
          value: t('benefits.quality.metrics.comfort.value'),
          label: t('benefits.quality.metrics.comfort.label')
        }
      },
      category: 'quality' as const
    }
  ];

  const implementationSteps = [
    {
      icon: <Activity className="w-6 h-6 text-purple-600" />,
      title: t('benefits.implementation.assessment.title'),
      description: t('benefits.implementation.assessment.description'),
      duration: t('benefits.implementation.assessment.duration')
    },
    {
      icon: <Zap className="w-6 h-6 text-blue-600" />,
      title: t('benefits.implementation.design.title'),
      description: t('benefits.implementation.design.description'),
      duration: t('benefits.implementation.design.duration')
    },
    {
      icon: <Lock className="w-6 h-6 text-green-600" />,
      title: t('benefits.implementation.installation.title'),
      description: t('benefits.implementation.installation.description'),
      duration: t('benefits.implementation.installation.duration')
    },
    {
      icon: <Users className="w-6 h-6 text-amber-600" />,
      title: t('benefits.implementation.training.title'),
      description: t('benefits.implementation.training.description'),
      duration: t('benefits.implementation.training.duration')
    }
  ];

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-12 sm:mb-16">
          <Badge variant="secondary" className="mb-4 px-3 py-1 text-sm bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800">
            <TrendingUp className="w-4 h-4 mr-2" />
            {t('benefits.badge')}
          </Badge>
          
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            {t('benefits.title')}
          </h2>
          
          <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed">
            {t('benefits.description')}
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 gap-6 lg:gap-8 mb-16">
          {benefits.map((benefit, index) => (
            <BenefitMetric
              key={index}
              icon={benefit.icon}
              title={benefit.title}
              description={benefit.description}
              metrics={benefit.metrics}
              category={benefit.category}
            />
          ))}
        </div>

        {/* Implementation Process */}
        <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 rounded-2xl p-8 sm:p-12 border border-purple-200 dark:border-purple-800">
          <div className="text-center mb-8">
            <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
              {t('benefits.implementation.title')}
            </h3>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              {t('benefits.implementation.description')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {implementationSteps.map((step, index) => (
              <div key={index} className="text-center bg-white dark:bg-gray-900 rounded-lg p-6 shadow-sm">
                <div className="flex justify-center mb-4">
                  {step.icon}
                </div>
                <h4 className="font-semibold text-foreground mb-2">{step.title}</h4>
                <p className="text-sm text-muted-foreground mb-3">{step.description}</p>
                <div className="text-xs text-purple-600 dark:text-purple-400 font-medium">{step.duration}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Compliance & Standards */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl sm:text-3xl font-bold text-foreground mb-8">
            {t('benefits.compliance.title')}
          </h3>
          
          <div className="grid md:grid-cols-4 gap-6">
            <Card className="p-6 bg-white dark:bg-gray-900 border border-green-200 dark:border-green-800">
              <CardContent className="p-0 text-center">
                <Award className="w-8 h-8 text-green-600 mx-auto mb-4" />
                <h4 className="font-semibold text-foreground mb-2">{t('benefits.compliance.gdpr.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('benefits.compliance.gdpr.description')}</p>
              </CardContent>
            </Card>

            <Card className="p-6 bg-white dark:bg-gray-900 border border-blue-200 dark:border-blue-800">
              <CardContent className="p-0 text-center">
                <Shield className="w-8 h-8 text-blue-600 mx-auto mb-4" />
                <h4 className="font-semibold text-foreground mb-2">{t('benefits.compliance.security.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('benefits.compliance.security.description')}</p>
              </CardContent>
            </Card>

            <Card className="p-6 bg-white dark:bg-gray-900 border border-purple-200 dark:border-purple-800">
              <CardContent className="p-0 text-center">
                <CheckCircle className="w-8 h-8 text-purple-600 mx-auto mb-4" />
                <h4 className="font-semibold text-foreground mb-2">{t('benefits.compliance.quality.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('benefits.compliance.quality.description')}</p>
              </CardContent>
            </Card>

            <Card className="p-6 bg-white dark:bg-gray-900 border border-amber-200 dark:border-amber-800">
              <CardContent className="p-0 text-center">
                <AlertTriangle className="w-8 h-8 text-amber-600 mx-auto mb-4" />
                <h4 className="font-semibold text-foreground mb-2">{t('benefits.compliance.safety.title')}</h4>
                <p className="text-sm text-muted-foreground">{t('benefits.compliance.safety.description')}</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
