'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface AccessibleSectionProps {
  children: React.ReactNode;
  className?: string;
  id?: string;
  ariaLabel?: string;
  ariaLabelledBy?: string;
  role?: string;
  as?: 'section' | 'main' | 'article' | 'aside' | 'header' | 'footer' | 'nav';
}

/**
 * AccessibleSection component that provides consistent accessibility features
 * and responsive design patterns for all service page sections.
 * 
 * Features:
 * - WCAG 2.1 AA compliant structure
 * - Proper semantic HTML
 * - Consistent responsive padding and spacing
 * - Focus management support
 * - Screen reader optimizations
 */
export function AccessibleSection({
  children,
  className,
  id,
  ariaLabel,
  ariaLabelledBy,
  role,
  as: Component = 'section'
}: AccessibleSectionProps) {
  return (
    <Component
      id={id}
      className={cn(
        // Base responsive padding following mobile-first approach
        "py-16 sm:py-20 lg:py-24",
        // Container spacing for consistent layout
        "px-4 sm:px-6 lg:px-8",
        // Focus management for keyboard navigation
        "focus-within:outline-none",
        // Custom classes
        className
      )}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledBy}
      role={role}
    >
      {children}
    </Component>
  );
}

interface AccessibleHeadingProps {
  children: React.ReactNode;
  level: 1 | 2 | 3 | 4 | 5 | 6;
  className?: string;
  id?: string;
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl';
}

/**
 * AccessibleHeading component that ensures proper heading hierarchy
 * and consistent typography across all service pages.
 */
export function AccessibleHeading({
  children,
  level,
  className,
  id,
  size
}: AccessibleHeadingProps) {
  const Component = `h${level}` as keyof JSX.IntrinsicElements;
  
  // Default size mapping based on heading level
  const defaultSizes = {
    1: '5xl',
    2: '4xl',
    3: '3xl',
    4: '2xl',
    5: 'xl',
    6: 'lg'
  };

  const sizeClasses = {
    'xs': 'text-xs',
    'sm': 'text-sm',
    'base': 'text-base',
    'lg': 'text-lg',
    'xl': 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl sm:text-4xl',
    '4xl': 'text-3xl sm:text-4xl lg:text-5xl',
    '5xl': 'text-4xl sm:text-5xl lg:text-6xl'
  };

  const appliedSize = size || defaultSizes[level];

  return (
    <Component
      id={id}
      className={cn(
        // Base heading styles
        "font-bold tracking-tight text-foreground leading-tight",
        // Responsive typography
        sizeClasses[appliedSize as keyof typeof sizeClasses],
        // Focus styles for accessibility
        "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-sm",
        // Custom classes
        className
      )}
    >
      {children}
    </Component>
  );
}

interface AccessibleButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  href?: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  disabled?: boolean;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  type?: 'button' | 'submit' | 'reset';
}

/**
 * AccessibleButton component that ensures WCAG 2.1 AA compliance
 * with proper touch targets, focus states, and keyboard navigation.
 */
export function AccessibleButton({
  children,
  onClick,
  href,
  variant = 'primary',
  size = 'md',
  className,
  disabled = false,
  ariaLabel,
  ariaDescribedBy,
  type = 'button'
}: AccessibleButtonProps) {
  const baseClasses = cn(
    // Base button styles
    "inline-flex items-center justify-center font-medium transition-colors",
    "focus:outline-none focus:ring-2 focus:ring-offset-2",
    "disabled:opacity-50 disabled:cursor-not-allowed",
    // Ensure minimum 44px touch target (WCAG 2.1 AA)
    "min-h-[44px] min-w-[44px]",
    // Rounded corners for better visual design
    "rounded-md"
  );

  const variantClasses = {
    primary: "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",
    secondary: "bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",
    outline: "border border-blue-200 text-blue-700 hover:bg-blue-50 focus:ring-blue-500",
    ghost: "text-blue-700 hover:bg-blue-50 focus:ring-blue-500"
  };

  const sizeClasses = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg"
  };

  const buttonClasses = cn(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    className
  );

  if (href) {
    return (
      <a
        href={href}
        className={buttonClasses}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
        role="button"
      >
        {children}
      </a>
    );
  }

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={buttonClasses}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
    >
      {children}
    </button>
  );
}

interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
}

/**
 * SkipLink component for keyboard navigation accessibility.
 * Allows users to skip to main content.
 */
export function SkipLink({ href, children }: SkipLinkProps) {
  return (
    <a
      href={href}
      className={cn(
        "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4",
        "bg-blue-600 text-white px-4 py-2 rounded-md z-50",
        "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      )}
    >
      {children}
    </a>
  );
}

/**
 * VisuallyHidden component for screen reader only content
 */
export function VisuallyHidden({ children }: { children: React.ReactNode }) {
  return (
    <span className="sr-only">
      {children}
    </span>
  );
}

/**
 * FocusTrap component for modal dialogs and complex interactions
 */
export function FocusTrap({ children, active = true }: { children: React.ReactNode; active?: boolean }) {
  const trapRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!active || !trapRef.current) return;

    const focusableElements = trapRef.current.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus();
          e.preventDefault();
        }
      }
    };

    document.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      document.removeEventListener('keydown', handleTabKey);
    };
  }, [active]);

  return (
    <div ref={trapRef}>
      {children}
    </div>
  );
}
