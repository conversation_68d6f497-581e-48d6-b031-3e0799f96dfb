import type { <PERSON>ada<PERSON> } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { Header } from '@/components/sections/header';
import { Footer } from '@/components/sections/footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Building, 
  MapPin, 
  Phone, 
  Globe, 
  Mail,
  FileText,
  Scale
} from 'lucide-react';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'impressum' });

  return {
    title: t('meta.title'),
    description: t('meta.description'),
    keywords: t('meta.keywords'),
    openGraph: {
      title: t('meta.title'),
      description: t('meta.description'),
      type: 'website',
      locale: locale,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function ImpressumPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  const t = await getTranslations('impressum');

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main>
        {/* Hero Section */}
        <section className="py-16 sm:py-20 lg:py-24 bg-muted/30">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-3xl mx-auto">
              <Badge variant="secondary" className="mb-4 px-3 py-1 text-sm">
                <FileText className="w-4 h-4 mr-2" />
                {t('sections.legal')}
              </Badge>
              
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground mb-6">
                {t('title')}
              </h1>
              
              <p className="text-lg sm:text-xl text-muted-foreground leading-relaxed">
                {t('companyInfo.title')}
              </p>
            </div>
          </div>
        </section>

        {/* Company Information Section */}
        <section className="py-16 sm:py-20 lg:py-24 bg-background">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
              <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
                
                {/* Company Details */}
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center text-xl">
                      <Building className="w-5 h-5 mr-3 text-primary" />
                      {t('sections.company')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h3 className="font-semibold text-lg text-foreground mb-2">
                        {t('companyInfo.companyName')}
                      </h3>
                    </div>
                  </CardContent>
                </Card>

                {/* Address */}
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center text-xl">
                      <MapPin className="w-5 h-5 mr-3 text-primary" />
                      {t('sections.address')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <p className="text-muted-foreground">{t('companyInfo.address.street')}</p>
                    <p className="text-muted-foreground">
                      {t('companyInfo.address.postalCode')} {t('companyInfo.address.city')}
                    </p>
                    <p className="text-muted-foreground">{t('companyInfo.address.country')}</p>
                  </CardContent>
                </Card>

                {/* Contact Information */}
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center text-xl">
                      <Phone className="w-5 h-5 mr-3 text-primary" />
                      {t('sections.contact')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Phone className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                      <a 
                        href={`tel:${t('companyInfo.contact.phone')}`}
                        className="text-primary hover:text-primary/80 transition-colors min-h-[44px] flex items-center"
                        aria-label={`Call ${t('companyInfo.contact.phone')}`}
                      >
                        {t('companyInfo.contact.phone')}
                      </a>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Globe className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                      <a 
                        href={`https://${t('companyInfo.contact.website')}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:text-primary/80 transition-colors min-h-[44px] flex items-center"
                        aria-label={`Visit ${t('companyInfo.contact.website')}`}
                      >
                        {t('companyInfo.contact.website')}
                      </a>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Mail className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                      <span className="text-muted-foreground">
                        {t('companyInfo.contact.email')}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                {/* Legal Information */}
                <Card className="h-fit">
                  <CardHeader>
                    <CardTitle className="flex items-center text-xl">
                      <Scale className="w-5 h-5 mr-3 text-primary" />
                      {t('sections.legal')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <p className="text-sm text-muted-foreground">
                      {t('companyInfo.legal.nip')}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {t('companyInfo.legal.krs')}
                    </p>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {t('companyInfo.legal.court')}
                    </p>
                    <Separator className="my-3" />
                    <p className="text-sm text-muted-foreground">
                      {t('companyInfo.legal.capital')}
                    </p>
                  </CardContent>
                </Card>

              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
