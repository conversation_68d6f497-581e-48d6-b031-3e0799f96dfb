import type { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { Header } from '@/components/sections/header';
import { Footer } from '@/components/sections/footer';
import { TelemonitoringHeroSection } from '@/components/sections/telemonitoring-hero';
import { TelemonitoringPlatformSection } from '@/components/sections/telemonitoring-platform';
import { TelemonitoringFeaturesSection } from '@/components/sections/telemonitoring-features';
import { TelemonitoringCTASection } from '@/components/sections/telemonitoring-cta';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'telemonitoring' });

  return {
    title: t('meta.title'),
    description: t('meta.description'),
    keywords: t('meta.keywords'),
    openGraph: {
      title: t('meta.title'),
      description: t('meta.description'),
      type: 'website',
      locale: locale,
    },
  };
}

export default async function TelemonitoringPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main>
        <TelemonitoringHeroSection />
        <TelemonitoringPlatformSection />
        <TelemonitoringFeaturesSection />
        <TelemonitoringCTASection />
      </main>
      <Footer />
    </div>
  );
}
