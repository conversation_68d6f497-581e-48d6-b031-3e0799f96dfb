import type { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { Header } from '@/components/sections/header';
import { Footer } from '@/components/sections/footer';
import { IoMTHeroSection } from '@/components/sections/iomt-hero';
import { IoMTFeaturesSection } from '@/components/sections/iomt-features';
import { IoMTComplianceSection } from '@/components/sections/iomt-compliance';
import { IoMTCTASection } from '@/components/sections/iomt-cta';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'iomt' });

  return {
    title: t('meta.title'),
    description: t('meta.description'),
    keywords: t('meta.keywords'),
    openGraph: {
      title: t('meta.title'),
      description: t('meta.description'),
      type: 'website',
      locale: locale,
    },
  };
}

export default async function IoMTPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main>
        <IoMTHeroSection />
        <IoMTFeaturesSection />
        <IoMTComplianceSection />
        <IoMTCTASection />
      </main>
      <Footer />
    </div>
  );
}
