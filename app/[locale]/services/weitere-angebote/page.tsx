import type { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { Header } from '@/components/sections/header';
import { Footer } from '@/components/sections/footer';
import { AdditionalServicesHeroSection } from '@/components/sections/additional-services-hero';
import { AdditionalServicesOffersSection } from '@/components/sections/additional-services-offers';
import { AdditionalServicesBenefitsSection } from '@/components/sections/additional-services-benefits';
import { AdditionalServicesCTASection } from '@/components/sections/additional-services-cta';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'additionalServices' });

  return {
    title: t('meta.title'),
    description: t('meta.description'),
    keywords: t('meta.keywords'),
    openGraph: {
      title: t('meta.title'),
      description: t('meta.description'),
      type: 'website',
      locale: locale,
    },
  };
}

export default async function AdditionalServicesPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main>
        <AdditionalServicesHeroSection />
        <AdditionalServicesOffersSection />
        <AdditionalServicesBenefitsSection />
        <AdditionalServicesCTASection />
      </main>
      <Footer />
    </div>
  );
}
