import type { Metadata } from 'next';
import { getTranslations, setRequestLocale } from 'next-intl/server';
import { Header } from '@/components/sections/header';
import { Footer } from '@/components/sections/footer';
import { DigitalizationHeroSection } from '@/components/sections/digitalization-hero';
import { DigitalizationServicesSection } from '@/components/sections/digitalization-services';
import { DigitalizationBenefitsSection } from '@/components/sections/digitalization-benefits';
import { DigitalizationCTASection } from '@/components/sections/digitalization-cta';

export async function generateMetadata({ params }: { params: Promise<{ locale: string }> }): Promise<Metadata> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'digitalization' });

  return {
    title: t('meta.title'),
    description: t('meta.description'),
    keywords: t('meta.keywords'),
    openGraph: {
      title: t('meta.title'),
      description: t('meta.description'),
      type: 'website',
      locale: locale,
    },
  };
}

export default async function DigitalizationPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main>
        <DigitalizationHeroSection />
        <DigitalizationServicesSection />
        <DigitalizationBenefitsSection />
        <DigitalizationCTASection />
      </main>
      <Footer />
    </div>
  );
}
